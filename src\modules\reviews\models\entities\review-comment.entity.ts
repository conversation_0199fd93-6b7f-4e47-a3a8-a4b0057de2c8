import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany, Index } from "typeorm";
import { Review } from "./review.entity";
import { CommentReaction } from "./comment-reaction.entity";

@Entity("review_comments")
export class ReviewComment {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	@Index()
	reviewId: string;

	@Column({ type: "uuid" })
	@Index()
	userId: string;

	@Column({ type: "text" })
	content: string;

	@Column({ type: "uuid", nullable: true })
	@Index()
	parentId?: string;

	@Column({ type: "integer", default: 0 })
	likes: number;

	@Column({ type: "integer", default: 0 })
	dislikes: number;

	@ManyToOne(() => Review, review => review.comments, { onDelete: "CASCADE" })
	@JoinColumn({ name: "reviewId" })
	review: Review;

	@ManyToOne(() => ReviewComment, comment => comment.replies, { onDelete: "SET NULL" })
	@JoinColumn({ name: "parentId" })
	parent?: ReviewComment;

	@OneToMany(() => ReviewComment, comment => comment.parent)
	replies: ReviewComment[];

	@OneToMany(() => CommentReaction, reaction => reaction.comment, { cascade: true })
	reactions: CommentReaction[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
