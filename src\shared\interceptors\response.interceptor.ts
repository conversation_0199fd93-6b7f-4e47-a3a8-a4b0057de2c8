import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { SuccessResponseDto } from "../dtos/success-response.dto";

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, SuccessResponseDto<T>> {
	intercept(context: ExecutionContext, next: CallHandler): Observable<SuccessResponseDto<T>> {
		return next.handle().pipe(
			map(data => {
				// Se a resposta já está no formato SuccessResponseDto, retorna como está
				if (data && typeof data === "object" && "success" in data) {
					return data;
				}

				// Formata a resposta no padrão SuccessResponseDto
				return {
					success: true,
					message: "Operação realizada com sucesso",
					data,
					meta: {
						timestamp: new Date().toISOString(),
					},
				};
			})
		);
	}
}
