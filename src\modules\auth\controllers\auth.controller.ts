import { Controller, Post, Body, HttpCode, HttpStatus, Get, UseGuards, Patch, Headers, UnauthorizedException } from "@nestjs/common";
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiExtraModels,
	ApiBadRequestResponse,
	ApiConflictResponse,
	ApiUnauthorizedResponse,
	ApiInternalServerErrorResponse,
	ApiBody,
	ApiBearerAuth,
} from "@nestjs/swagger";
import { LoginUserUseCase } from "../use-cases/login-user/login-user.use-case";
import { RegisterUserUseCase } from "../use-cases/register-user/register-user.use-case";
import { RefreshTokenUseCase } from "../use-cases/refresh-token/refresh-token.use-case";
import { ChangePasswordUseCase } from "../use-cases/change-password/change-password.use-case";
import { GetProfileUseCase } from "../use-cases/get-profile/get-profile.use-case";
import { LoginDto } from "../models/dtos/login.dto";
import { RegisterDto } from "../models/dtos/register.dto";
import { ChangePasswordDto } from "../models/dtos/change-password.dto";
import { AuthResponseDto } from "../models/dtos/auth-response.dto";
import { UserResponseDto } from "../../user/models/dtos/user-response.dto";
import { JwtAuthGuard } from "../models/guard/jwt-auth.guard";
import { CurrentUser, ICurrentUserPayload } from "../../../shared/decorators/current-user.decorator";
import {
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";

@ApiTags("auth")
@ApiExtraModels(
	AuthResponseDto,
	UserResponseDto,
	ChangePasswordDto,
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	InternalServerErrorResponseDto
)
@Controller("auth")
export class AuthController {
	constructor(
		private readonly loginUserUseCase: LoginUserUseCase,
		private readonly registerUserUseCase: RegisterUserUseCase,
		private readonly refreshTokenUseCase: RefreshTokenUseCase,
		private readonly changePasswordUseCase: ChangePasswordUseCase,
		private readonly getProfileUseCase: GetProfileUseCase
	) {}

	@Post("register")
	@HttpCode(HttpStatus.CREATED)
	@ApiOperation({
		summary: "📝 Registrar novo usuário",
		description: `
### 📋 Descrição
Cria uma nova conta de usuário no sistema com validação completa dos dados.

### 🔍 Validações Aplicadas
- **Username**: Único, entre 3-50 caracteres
- **Email**: Formato válido e único no sistema
- **Senha**: Mínimo 6 caracteres
- **Nome Completo**: Opcional, máximo 100 caracteres
- **Avatar**: URL válida (opcional)

### 💡 Dicas
- O sistema verifica automaticamente duplicatas de email/username
- A senha é criptografada antes do armazenamento
- Um token JWT é retornado automaticamente após o registro
		`,
		operationId: "registerUser",
	})
	@ApiBody({
		type: RegisterDto,
		description: "Dados necessários para criar uma nova conta",
		examples: {
			basicUser: {
				summary: "Usuário Básico",
				description: "Exemplo de registro com dados mínimos obrigatórios",
				value: {
					username: "joao_silva",
					email: "<EMAIL>",
					password: "MinhaSenh@123",
				},
			},
			completeUser: {
				summary: "Usuário Completo",
				description: "Exemplo de registro com todos os dados opcionais",
				value: {
					username: "maria_santos",
					email: "<EMAIL>",
					password: "SenhaSegura@456",
					fullName: "Maria Santos Silva",
					avatar: "https://example.com/avatars/maria.jpg",
				},
			},
		},
	})
	@ApiResponse({
		status: 201,
		description: "✅ Usuário registrado com sucesso. Token JWT retornado para acesso imediato.",
		type: AuthResponseDto,
		schema: {
			example: {
				access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				token_type: "Bearer",
				expires_in: 604800,
				user: {
					id: 1,
					username: "joao_silva",
					email: "<EMAIL>",
					fullName: "João Silva",
					avatar: null,
					isActive: true,
					createdAt: "2024-06-12T10:30:00.000Z",
					updatedAt: "2024-06-12T10:30:00.000Z",
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados inválidos fornecidos",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["username deve ter pelo menos 3 caracteres", "email deve ser um email válido", "password deve ter pelo menos 6 caracteres"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/register",
			},
		},
	})
	@ApiConflictResponse({
		description: "⚠️ Email ou nome de usuário já existe no sistema",
		type: ConflictErrorResponseDto,
		schema: {
			example: {
				statusCode: 409,
				message: "Email ou nome de usuário já está em uso",
				error: "Conflict",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/register",
				details: {
					field: "email",
					value: "<EMAIL>",
					reason: "Este email já está cadastrado no sistema",
				},
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
		schema: {
			example: {
				statusCode: 500,
				message: "Erro interno do servidor. Tente novamente mais tarde.",
				error: "Internal Server Error",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/register",
				errorId: "err_123456789",
			},
		},
	})
	register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
		return this.registerUserUseCase.execute(registerDto);
	}

	@Post("login")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "🔑 Realizar login",
		description: `
### 📋 Descrição
Autentica um usuário existente e retorna um token JWT para acesso à API.

### 🔍 Processo de Autenticação
1. **Verificação**: Valida username/email e senha
2. **Autenticação**: Compara senha com hash armazenado
3. **Token JWT**: Gera token com validade de 7 dias
4. **Resposta**: Retorna token e dados do usuário

### 🔑 Formatos de Login Aceitos
- **Username**: \`joao_silva\`
- **Email**: \`<EMAIL>\`

### ⚡ Token JWT
- **Validade**: 7 dias (604.800 segundos)
- **Formato**: Bearer Token
- **Uso**: Header \`Authorization: Bearer {token}\`
		`,
		operationId: "loginUser",
	})
	@ApiBody({
		type: LoginDto,
		description: "Credenciais para autenticação",
		examples: {
			withUsername: {
				summary: "Login com Username",
				description: "Fazer login usando nome de usuário",
				value: {
					usernameOrEmail: "joao_silva",
					password: "MinhaSenh@123",
				},
			},
			withEmail: {
				summary: "Login com Email",
				description: "Fazer login usando endereço de email",
				value: {
					usernameOrEmail: "<EMAIL>",
					password: "MinhaSenh@123",
				},
			},
		},
	})
	@ApiResponse({
		status: 200,
		description: "✅ Login realizado com sucesso. Token JWT gerado.",
		type: AuthResponseDto,
		schema: {
			example: {
				access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				token_type: "Bearer",
				expires_in: 604800,
				user: {
					id: 1,
					username: "joao_silva",
					email: "<EMAIL>",
					fullName: "João Silva",
					avatar: "https://example.com/avatars/joao.jpg",
					isActive: true,
					createdAt: "2024-06-10T08:00:00.000Z",
					updatedAt: "2024-06-12T10:30:00.000Z",
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados de login inválidos",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["usernameOrEmail é obrigatório", "password é obrigatório"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/login",
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Credenciais inválidas ou usuário não encontrado",
		type: UnauthorizedErrorResponseDto,
		schema: {
			example: {
				statusCode: 401,
				message: "Credenciais inválidas",
				error: "Unauthorized",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/login",
				code: "INVALID_CREDENTIALS",
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
		schema: {
			example: {
				statusCode: 500,
				message: "Erro interno do servidor. Tente novamente mais tarde.",
				error: "Internal Server Error",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/login",
				errorId: "err_987654321",
			},
		},
	})
	login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
		return this.loginUserUseCase.execute(loginDto);
	}
	@Post("refresh")
	@HttpCode(HttpStatus.OK)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "🔄 Renovar token de acesso",
		description: `
### 📋 Descrição
Renova o token de acesso usando um refresh token válido enviado no header Authorization.

### 🔍 Processo de Renovação
1. **Validação**: Verifica se o refresh token é válido
2. **Verificação**: Confirma se o token está armazenado no banco
3. **Geração**: Cria novos access e refresh tokens
4. **Atualização**: Salva o novo refresh token no banco

### 🔐 Autenticação
O refresh token deve ser enviado no header Authorization no formato:
\`Authorization: Bearer {refresh_token}\`

### ⚡ Vantagens
- **Segurança**: Tokens de acesso com vida curta
- **Conveniência**: Renovação automática sem re-login
- **Controle**: Possibilidade de revogar refresh tokens
		`,
		operationId: "refreshToken",
	})
	@ApiResponse({
		status: 200,
		description: "✅ Tokens renovados com sucesso",
		type: AuthResponseDto,
		schema: {
			example: {
				access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				token_type: "Bearer",
				expires_in: 604800,
				user: {
					id: 1,
					username: "joao_silva",
					email: "<EMAIL>",
					fullName: "João Silva",
					avatar: "https://example.com/avatars/joao.jpg",
					isActive: true,
					createdAt: "2024-06-10T08:00:00.000Z",
					updatedAt: "2024-06-12T10:30:00.000Z",
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Header Authorization não fornecido",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: "Header Authorization com refresh token é obrigatório",
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/refresh",
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Refresh token expirado ou inválido",
		type: UnauthorizedErrorResponseDto,
		schema: {
			example: {
				statusCode: 401,
				message: "Token de refresh inválido ou expirado",
				error: "Unauthorized",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/refresh",
				code: "INVALID_REFRESH_TOKEN",
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	refresh(@Headers("authorization") authorization: string): Promise<AuthResponseDto> {
		if (!authorization) {
			throw new UnauthorizedException("Header Authorization com refresh token é obrigatório");
		}

		// Extrair o token do header Authorization (Bearer token)
		const refreshToken = authorization.replace("Bearer ", "");

		if (!refreshToken || refreshToken === authorization) {
			throw new UnauthorizedException("Formato do header Authorization inválido. Use: Bearer {refresh_token}");
		}

		return this.refreshTokenUseCase.execute(refreshToken);
	}

	@Get("profile")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "👤 Obter perfil do usuário",
		description: `
### 📋 Descrição
Retorna as informações do perfil do usuário autenticado.

### 🔐 Autenticação Necessária
Este endpoint requer autenticação JWT válida.

### 📊 Dados Retornados
- Informações atualizadas do usuário logado
- Todos os campos do perfil (exceto senha)
- Timestamps de criação e atualização

### 💡 Uso Recomendado
- Verificar dados atuais do usuário
- Exibir informações no frontend
- Validar se o token ainda é válido
		`,
		operationId: "getProfile",
	})
	@ApiResponse({
		status: 200,
		description: "✅ Perfil obtido com sucesso",
		type: UserResponseDto,
		schema: {
			example: {
				id: 1,
				username: "joao_silva",
				email: "<EMAIL>",
				fullName: "João Silva",
				avatar: "https://example.com/avatars/joao.jpg",
				isActive: true,
				createdAt: "2024-06-10T08:00:00.000Z",
				updatedAt: "2024-06-12T10:30:00.000Z",
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	getProfile(@CurrentUser() currentUser: ICurrentUserPayload): Promise<UserResponseDto> {
		return this.getProfileUseCase.execute(currentUser);
	}

	@Patch("change-password")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "🔐 Alterar senha",
		description: `
### 📋 Descrição
Permite ao usuário autenticado alterar sua senha atual.

### 🔐 Autenticação Necessária
Este endpoint requer autenticação JWT válida.

### 🔍 Validações Aplicadas
- **Senha Atual**: Deve estar correta
- **Nova Senha**: Mínimo 6 caracteres
- **Diferença**: Nova senha deve ser diferente da atual

### 🛡️ Segurança
- Todos os refresh tokens são invalidados após a alteração
- Usuário precisará fazer login novamente
- Senha é criptografada antes do armazenamento

### ⚠️ Importante
Após alterar a senha, será necessário fazer login novamente.
		`,
		operationId: "changePassword",
	})
	@ApiBody({
		type: ChangePasswordDto,
		description: "Dados para alteração de senha",
		examples: {
			changePassword: {
				summary: "Alterar Senha",
				description: "Exemplo de alteração de senha",
				value: {
					currentPassword: "MinhaSenh@123",
					newPassword: "NovaSenha@456",
				},
			},
		},
	})
	@ApiResponse({
		status: 200,
		description: "✅ Senha alterada com sucesso",
		schema: {
			example: {
				message: "Senha alterada com sucesso. Faça login novamente com a nova senha.",
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados inválidos fornecidos",
		type: ValidationErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Senha atual incorreta ou token inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	changePassword(@CurrentUser() currentUser: ICurrentUserPayload, @Body() changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
		return this.changePasswordUseCase.execute(currentUser, changePasswordDto);
	}
}
