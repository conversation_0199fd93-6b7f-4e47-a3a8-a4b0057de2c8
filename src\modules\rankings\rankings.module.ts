import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Ranking, RankingItem } from "./models/entities";
import { RankingRepository, RankingItemRepository } from "./repositories";
import {
	CreateRankingUseCase,
	UpdateRankingUseCase,
	DeleteRankingUseCase,
	GetRankingUseCase,
	ListUserRankingsUseCase,
	AddItemToRankingUseCase,
	UpdateRankingItemUseCase,
	RemoveItemFromRankingUseCase,
	GetRankingItemsUseCase,
} from "./use-cases";
import { RankingsController } from "./controllers";
import { WorksModule } from "../works/works.module";

@Module({
	imports: [
		TypeOrmModule.forFeature([Ranking, RankingItem]),
		forwardRef(() => WorksModule), // Para ter acesso ao WorkRepository
	],
	controllers: [RankingsController],
	providers: [
		RankingRepository,
		RankingItemRepository,
		CreateRankingUseCase,
		UpdateRankingUseCase,
		DeleteRankingUseCase,
		GetRankingUseCase,
		ListUserRankingsUseCase,
		AddItemToRankingUseCase,
		UpdateRankingItemUseCase,
		RemoveItemFromRankingUseCase,
		GetRankingItemsUseCase,
		{
			provide: "IRankingRepository",
			useExisting: RankingRepository,
		},
		{
			provide: "IRankingItemRepository",
			useExisting: RankingItemRepository,
		},
	],
	exports: [
		"IRankingRepository",
		"IRankingItemRepository",
		RankingRepository,
		RankingItemRepository,
		CreateRankingUseCase,
		UpdateRankingUseCase,
		DeleteRankingUseCase,
		GetRankingUseCase,
		ListUserRankingsUseCase,
		AddItemToRankingUseCase,
		UpdateRankingItemUseCase,
		RemoveItemFromRankingUseCase,
		GetRankingItemsUseCase,
	],
})
export class RankingsModule {}
