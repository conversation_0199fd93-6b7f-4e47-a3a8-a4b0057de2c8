import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { ReviewComment } from "../models/entities/review-comment.entity";
import { IReviewComment, IReviewCommentFilters, IReviewCommentRepository } from "../models/interfaces";

@Injectable()
export class ReviewCommentRepository implements IReviewCommentRepository {
	constructor(
		@InjectRepository(ReviewComment)
		private readonly commentRepository: Repository<ReviewComment>
	) {}

	async create(comment: Partial<IReviewComment>): Promise<IReviewComment> {
		const newComment = this.commentRepository.create(comment);
		return await this.commentRepository.save(newComment);
	}

	async update(id: string, comment: Partial<IReviewComment>): Promise<IReviewComment> {
		await this.commentRepository.update(id, comment);
		return await this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.commentRepository.delete(id);
	}

	async findById(id: string): Promise<IReviewComment> {
		return await this.commentRepository.findOne({
			where: { id },
			relations: ["replies"],
		});
	}

	async findAll(filters: IReviewCommentFilters): Promise<IReviewComment[]> {
		const query = this.buildFilterQuery(filters);

		// Ordenação
		if (filters.sort) {
			query.orderBy(`comment.${filters.sort}`, filters.order || "DESC");
		} else {
			query.orderBy("comment.createdAt", "DESC");
		}

		// Paginação
		if (filters.skip !== undefined) {
			query.skip(filters.skip);
		}

		if (filters.take !== undefined) {
			query.take(filters.take);
		}

		return await query.getMany();
	}

	async findCount(filters: IReviewCommentFilters): Promise<number> {
		const query = this.buildFilterQuery(filters);
		return await query.getCount();
	}

	private buildFilterQuery(filters: IReviewCommentFilters): SelectQueryBuilder<ReviewComment> {
		const query = this.commentRepository.createQueryBuilder("comment");

		if (filters.reviewId) {
			query.andWhere("comment.reviewId = :reviewId", { reviewId: filters.reviewId });
		}

		if (filters.userId) {
			query.andWhere("comment.userId = :userId", { userId: filters.userId });
		}

		if (filters.parentId !== undefined) {
			if (filters.parentId === null) {
				// Buscar apenas comentários de primeiro nível
				query.andWhere("comment.parentId IS NULL");
			} else {
				// Buscar respostas a um comentário específico
				query.andWhere("comment.parentId = :parentId", { parentId: filters.parentId });
			}
		}

		return query;
	}

	async incrementLikes(id: string): Promise<void> {
		await this.commentRepository.increment({ id }, "likes", 1);
	}

	async decrementLikes(id: string): Promise<void> {
		await this.commentRepository.decrement({ id }, "likes", 1);
	}

	async incrementDislikes(id: string): Promise<void> {
		await this.commentRepository.increment({ id }, "dislikes", 1);
	}

	async decrementDislikes(id: string): Promise<void> {
		await this.commentRepository.decrement({ id }, "dislikes", 1);
	}

	async findByReviewId(reviewId: string, includeReplies: boolean = false): Promise<IReviewComment[]> {
		const query = this.commentRepository.createQueryBuilder("comment").where("comment.reviewId = :reviewId", { reviewId });

		if (!includeReplies) {
			query.andWhere("comment.parentId IS NULL");
		}

		query.orderBy("comment.createdAt", "ASC");

		if (includeReplies) {
			query.leftJoinAndSelect("comment.replies", "replies");
		}

		return await query.getMany();
	}

	async findRepliesByCommentId(commentId: string): Promise<IReviewComment[]> {
		return await this.commentRepository.find({
			where: { parentId: commentId },
			order: { createdAt: "ASC" },
		});
	}
}
