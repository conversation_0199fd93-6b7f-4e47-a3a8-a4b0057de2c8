import { Injectable, Inject, NotFoundException, ConflictException } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IRoleRepository, IUserRoleRepository } from "../../models/interfaces";
import { IUserRepository } from "../../../user/models/interfaces/user-repository.interface";
import { AssignRoleDto } from "../../models/dtos";
import { UserRoleWithDetailsResponseDto } from "../../models/dtos";

/**
 * Use case para atribuir um role a um usuário
 */
@Injectable()
export class AssignRoleUseCase extends BaseUseCase<AssignRoleDto, UserRoleWithDetailsResponseDto> {
	constructor(
		@Inject("IRoleRepository")
		private readonly roleRepository: IRoleRepository,
		@Inject("IUserRoleRepository")
		private readonly userRoleRepository: IUserRoleRepository,
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {
		super("AssignRoleUseCase");
	}

	/**
	 * Executa a atribuição de um role a um usuário
	 *
	 * @param assignRoleDto - Dados para atribuição do role
	 * @param assignedBy - ID do usuário que está fazendo a atribuição
	 * @returns Relação usuário-role criada
	 */
	async execute(assignRoleDto: AssignRoleDto, assignedBy?: number): Promise<UserRoleWithDetailsResponseDto> {
		this.logger.log(`Iniciando atribuição de role ${assignRoleDto.roleId} para usuário ${assignRoleDto.userId}`);

		try {
			// Verifica se o usuário existe
			const user = await this.userRepository.findById(assignRoleDto.userId);
			if (!user) {
				throw new NotFoundException(`Usuário com ID ${assignRoleDto.userId} não encontrado`);
			}

			// Verifica se o role existe
			const role = await this.roleRepository.findById(assignRoleDto.roleId);
			if (!role) {
				throw new NotFoundException(`Role com ID ${assignRoleDto.roleId} não encontrado`);
			}

			if (!role.isActive) {
				throw new ConflictException(`Role '${role.name}' está inativo e não pode ser atribuído`);
			}

			// Verifica se o usuário já possui este role
			const existingUserRole = await this.userRoleRepository.findByUserAndRole(assignRoleDto.userId, assignRoleDto.roleId);

			if (existingUserRole) {
				throw new ConflictException(`Usuário já possui o role '${role.name}'`);
			}

			// Converte a data de expiração se fornecida
			const expiresAt = assignRoleDto.expiresAt ? new Date(assignRoleDto.expiresAt) : undefined;

			// Cria a atribuição do role
			const userRole = await this.userRoleRepository.assignRole(assignRoleDto.userId, assignRoleDto.roleId, assignedBy, expiresAt);

			this.logger.log(`Role '${role.name}' atribuído com sucesso ao usuário ${user.username}`);

			// Busca a relação criada com detalhes do role
			const userRoleWithDetails = await this.userRoleRepository.findById(userRole.id);

			return this.mapToResponseDto(userRoleWithDetails);
		} catch (error) {
			this.logger.error(`Erro ao atribuir role: ${error.message}`, error.stack);
			throw error;
		}
	}

	/**
	 * Mapeia a entidade UserRole para o DTO de resposta
	 */
	private mapToResponseDto(userRole: any): UserRoleWithDetailsResponseDto {
		return {
			id: userRole.id,
			userId: userRole.userId,
			roleId: userRole.roleId,
			isActive: userRole.isActive,
			expiresAt: userRole.expiresAt,
			assignedBy: userRole.assignedBy,
			createdAt: userRole.createdAt,
			updatedAt: userRole.updatedAt,
			role: {
				id: userRole.role.id,
				name: userRole.role.name,
				description: userRole.role.description,
				isActive: userRole.role.isActive,
				isSystem: userRole.role.isSystem,
				createdAt: userRole.role.createdAt,
				updatedAt: userRole.role.updatedAt,
			},
		};
	}
}
