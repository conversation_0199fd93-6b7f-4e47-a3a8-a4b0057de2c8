import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
	IsUUID,
	IsEnum,
	IsOptional,
	IsInt,
	IsNumber,
	Min,
	Max,
} from 'class-validator';
import { ReadingStatus } from '../enums';

export class AddToReadingDto {
	@ApiProperty({
		description: 'ID da obra',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@IsUUID()
	workId: string;

	@ApiProperty({
		description: 'Status de leitura',
		enum: ReadingStatus,
		example: ReadingStatus.READING,
	})
	@IsEnum(ReadingStatus)
	status: ReadingStatus;

	@ApiPropertyOptional({
		description: 'Capítulo atual',
		example: 15,
		minimum: 0,
	})
	@IsOptional()
	@IsInt()
	@Min(0)
	currentChapter?: number;

	@ApiPropertyOptional({
		description: 'Avaliação pessoal (0-5)',
		example: 4.5,
		minimum: 0,
		maximum: 5,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	@Max(5)
	personalRating?: number;
}
