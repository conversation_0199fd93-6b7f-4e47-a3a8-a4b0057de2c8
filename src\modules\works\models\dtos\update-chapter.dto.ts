import { ApiPropertyOptional } from '@nestjs/swagger';
import {
	IsString,
	IsOptional,
	IsInt,
	IsDateString,
	Min,
	MaxLength,
	IsUrl,
} from 'class-validator';

export class UpdateChapterDto {
	@ApiPropertyOptional({
		description: 'Número do capítulo',
		example: 1,
		minimum: 1,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	number?: number;

	@ApiPropertyOptional({
		description: 'Título do capítulo',
		example: 'O Despertar',
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	title?: string;

	@ApiPropertyOptional({
		description: 'Data de lançamento do capítulo',
		example: '2024-01-15',
	})
	@IsOptional()
	@IsDateString()
	releaseDate?: Date;

	@ApiPropertyOptional({
		description: 'URL do capítulo',
		example: 'https://example.com/chapter/1',
	})
	@IsOptional()
	@IsUrl()
	url?: string;
}
