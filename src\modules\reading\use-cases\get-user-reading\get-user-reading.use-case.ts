import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IUserWork, IUserWorkRepository } from "../../models/interfaces";

@Injectable()
export class GetUserReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string, userWorkId: string): Promise<IUserWork> {
		// Verificar se o registro existe e pertence ao usuário
		const userWork = await this.userWorkRepository.findById(userWorkId);
		if (!userWork) {
			throw new ResourceNotFoundException("Registro de leitura", userWorkId);
		}
		if (userWork.userId !== userId) {
			throw new ResourceNotFoundException("Registro de leitura do usuário", `${userWorkId}:${userId}`);
		}

		return userWork;
	}
}
