import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsUrl, MaxLength, IsEnum } from "class-validator";
import { UserRole } from "../enums";

export class UpdateUserDto {
	@ApiProperty({
		description: "Nome completo do usuário",
		example: "<PERSON>",
		maxLength: 100,
		required: false,
		examples: {
			update: {
				summary: "Atualizar Nome",
				value: "<PERSON>",
			},
			professional: {
				summary: "Título Profissional",
				value: "Dr. <PERSON>",
			},
			clear: {
				summary: "Limpar Nome",
				value: null,
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Nome completo deve ser uma string" })
	@MaxLength(100, { message: "Nome completo deve ter no máximo 100 caracteres" })
	fullName?: string;

	@ApiProperty({
		description: "URL da nova imagem de avatar",
		example: "https://example.com/new-avatar.jpg",
		format: "uri",
		required: false,
		examples: {
			newPhoto: {
				summary: "Nova Foto",
				value: "https://cdn.example.com/avatars/new-photo.jpg",
			},
			gravatar: {
				summary: "Gravatar",
				value: "https://www.gravatar.com/avatar/hash",
			},
			remove: {
				summary: "Remover Avatar",
				value: null,
			},
		},
	})
	@IsOptional()
	@IsUrl({}, { message: "Avatar deve ser uma URL válida" })
	avatar?: string;

	@ApiProperty({
		description: "Role/função do usuário no sistema (apenas para administradores)",
		example: UserRole.USER,
		enum: UserRole,
		enumName: "UserRole",
		required: false,
		examples: {
			promote: {
				summary: "Promover a Moderador",
				value: UserRole.MODERATOR,
			},
			demote: {
				summary: "Rebaixar a Usuário",
				value: UserRole.USER,
			},
			admin: {
				summary: "Promover a Admin",
				value: UserRole.ADMIN,
			},
		},
	})
	@IsOptional()
	@IsEnum(UserRole, { message: "Role deve ser um valor válido" })
	role?: UserRole;
}
