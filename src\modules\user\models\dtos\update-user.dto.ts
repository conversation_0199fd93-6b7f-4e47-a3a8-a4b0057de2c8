import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsUrl, MaxLength } from "class-validator";

export class UpdateUserDto {
	@ApiProperty({
		description: "Nome completo do usuário",
		example: "<PERSON>",
		maxLength: 100,
		required: false,
		examples: {
			update: {
				summary: "Atualizar Nome",
				value: "<PERSON>",
			},
			professional: {
				summary: "Título Profissional",
				value: "Dr. <PERSON>",
			},
			clear: {
				summary: "Limpar Nome",
				value: null,
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Nome completo deve ser uma string" })
	@MaxLength(100, { message: "Nome completo deve ter no máximo 100 caracteres" })
	fullName?: string;

	@ApiProperty({
		description: "URL da nova imagem de avatar",
		example: "https://example.com/new-avatar.jpg",
		format: "uri",
		required: false,
		examples: {
			newPhoto: {
				summary: "Nova Foto",
				value: "https://cdn.example.com/avatars/new-photo.jpg",
			},
			gravatar: {
				summary: "Gravatar",
				value: "https://www.gravatar.com/avatar/hash",
			},
			remove: {
				summary: "Remover Avatar",
				value: null,
			},
		},
	})
	@IsOptional()
	@IsUrl({}, { message: "Avatar deve ser uma URL válida" })
	avatar?: string;
}
