import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from "typeorm";
import { RankingItem } from "./ranking-item.entity";

@Entity("rankings")
@Index(["userId", "name"], { unique: true }) // Um usuário não pode ter dois rankings com o mesmo nome
export class Ranking {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	userId: string;

	@Column({ type: "varchar", length: 100 })
	name: string;

	@Column({ type: "text", nullable: true })
	description?: string;

	@Column({ type: "boolean", default: false })
	isPublic: boolean;

	@Column({ type: "varchar", length: 255, nullable: true })
	coverImage?: string;

	@Column({ type: "integer", default: 0 })
	itemsCount: number;

	@OneToMany(() => RankingItem, rankingItem => rankingItem.ranking, { cascade: true })
	items: RankingItem[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
