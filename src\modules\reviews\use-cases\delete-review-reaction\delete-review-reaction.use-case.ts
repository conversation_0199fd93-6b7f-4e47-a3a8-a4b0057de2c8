import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IReviewReactionRepository, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class DeleteReviewReactionUseCase {
	constructor(
		@Inject("IReviewReactionRepository")
		private readonly reactionRepository: IReviewReactionRepository,

		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(reviewId: string, userId: string): Promise<void> {
		// Verificar se a reação existe
		const reaction = await this.reactionRepository.findByReviewAndUser(reviewId, userId);
		if (!reaction) {
			throw new ResourceNotFoundException("Reação", `${reviewId}:${userId}`);
		}

		// Atualizar os contadores na review
		if (reaction.isLike) {
			await this.reviewRepository.decrementLikes(reviewId);
		} else {
			await this.reviewRepository.decrementDislikes(reviewId);
		}

		// Excluir a reação
		await this.reactionRepository.delete(reaction.id);
	}
}
