import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsDateString, IsEnum, IsInt, IsOptional, IsString, Max, Min, ValidateNested } from "class-validator";

export enum SortDirection {
	ASC = "ASC",
	DESC = "DESC",
}

export class SortDto {
	@ApiProperty({
		description: "Campo para ordenação",
		example: "createdAt",
	})
	@IsString()
	field: string;

	@ApiProperty({
		description: "Direção da ordenação",
		enum: SortDirection,
		example: SortDirection.DESC,
	})
	@IsEnum(SortDirection)
	direction: SortDirection;
}

export class PaginationDto {
	@ApiPropertyOptional({
		description: "Número da página (começando em 1)",
		minimum: 1,
		default: 1,
		example: 1,
	})
	@IsOptional()
	@IsInt({ message: "Página deve ser um número inteiro" })
	@Min(1, { message: "P<PERSON>gina deve ser maior que 0" })
	@Type(() => Number)
	page?: number = 1;

	@ApiPropertyOptional({
		description: "Número de itens por página",
		minimum: 1,
		maximum: 100,
		default: 20,
		example: 20,
	})
	@IsOptional()
	@IsInt({ message: "Limit deve ser um número inteiro" })
	@Min(1, { message: "Limit deve ser maior que 0" })
	@Max(100, { message: "Limit deve ser menor ou igual a 100" })
	@Type(() => Number)
	limit?: number = 20;

	@ApiPropertyOptional({
		description: "Configurações de ordenação",
		type: [SortDto],
	})
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => SortDto)
	sort?: SortDto[];
}

export class DateRangeDto {
	@ApiPropertyOptional({
		description: "Data de início (ISO 8601)",
		example: "2023-01-01T00:00:00.000Z",
	})
	@IsOptional()
	@IsDateString({}, { message: "Data de início deve ser uma data válida" })
	startDate?: string;

	@ApiPropertyOptional({
		description: "Data de fim (ISO 8601)",
		example: "2023-12-31T23:59:59.999Z",
	})
	@IsOptional()
	@IsDateString({}, { message: "Data de fim deve ser uma data válida" })
	endDate?: string;
}

// Base para DTOs de busca/filtro
export class BaseFilterDto extends PaginationDto {
	@ApiPropertyOptional({
		description: "Termo de busca geral",
		example: "termo de busca",
	})
	@IsOptional()
	@IsString()
	search?: string;

	@ApiPropertyOptional({
		description: "Filtro por intervalo de datas",
		type: DateRangeDto,
	})
	@IsOptional()
	@ValidateNested()
	@Type(() => DateRangeDto)
	dateRange?: DateRangeDto;
}
