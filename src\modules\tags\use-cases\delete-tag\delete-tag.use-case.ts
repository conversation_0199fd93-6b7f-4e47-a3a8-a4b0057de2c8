import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { ITagRepository } from "../../models/interfaces";

@Injectable()
export class DeleteTagUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(id: string): Promise<void> {
		const tag = await this.tagRepository.findById(id);

		if (!tag) {
			throw new ResourceNotFoundException("Tag", id);
		}

		await this.tagRepository.delete(id);
	}
}
