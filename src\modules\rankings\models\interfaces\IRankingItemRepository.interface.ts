import { IRankingItem } from "./IRankingItem.interface";

export interface IRankingItemFilters {
	workId?: string;
	minRating?: number;
	maxRating?: number;
	sortBy?: "position" | "rating" | "createdAt";
	sortOrder?: "ASC" | "DESC";
}

export interface IRankingItemRepository {
	create(rankingItem: Partial<IRankingItem>): Promise<IRankingItem>;
	update(id: string, rankingItem: Partial<IRankingItem>): Promise<IRankingItem>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<IRankingItem>;
	findByRankingId(rankingId: string, filters?: IRankingItemFilters): Promise<IRankingItem[]>;
	findByRankingIdAndWorkId(rankingId: string, workId: string): Promise<IRankingItem>;
	reorderItems(rankingId: string, reorderedItems: { id: string; position: number }[]): Promise<void>;
}
