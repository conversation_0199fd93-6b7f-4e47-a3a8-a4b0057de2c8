import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, In, SelectQueryBuilder } from "typeorm";
import { RankingItem } from "../models/entities/ranking-item.entity";
import { IRankingItem, IRankingItemFilters, IRankingItemRepository } from "../models/interfaces";

@Injectable()
export class RankingItemRepository implements IRankingItemRepository {
	constructor(
		@InjectRepository(RankingItem)
		private readonly rankingItemRepository: Repository<RankingItem>
	) {}

	async create(rankingItem: Partial<IRankingItem>): Promise<IRankingItem> {
		const newRankingItem = this.rankingItemRepository.create(rankingItem);
		return await this.rankingItemRepository.save(newRankingItem);
	}

	async update(id: string, rankingItem: Partial<IRankingItem>): Promise<IRankingItem> {
		await this.rankingItemRepository.update(id, rankingItem);
		return await this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.rankingItemRepository.delete(id);
	}

	async findById(id: string): Promise<IRankingItem> {
		return await this.rankingItemRepository.findOne({
			where: { id },
		});
	}

	async findByRankingId(rankingId: string, filters?: IRankingItemFilters): Promise<IRankingItem[]> {
		const queryBuilder = this.rankingItemRepository.createQueryBuilder("rankingItem");

		queryBuilder.where("rankingItem.rankingId = :rankingId", { rankingId });

		if (filters) {
			this.applyFilters(queryBuilder, filters);
		}

		// Definir ordenação padrão
		const sortBy = filters?.sortBy || "position";
		const sortOrder = filters?.sortOrder || "ASC";

		queryBuilder.orderBy(`rankingItem.${sortBy}`, sortOrder);

		return await queryBuilder.getMany();
	}

	async findByRankingIdAndWorkId(rankingId: string, workId: string): Promise<IRankingItem> {
		return await this.rankingItemRepository.findOne({
			where: {
				rankingId,
				workId,
			},
		});
	}

	async reorderItems(rankingId: string, reorderedItems: { id: string; position: number }[]): Promise<void> {
		const ids = reorderedItems.map(item => item.id);
		const items = await this.rankingItemRepository.find({
			where: { id: In(ids), rankingId },
		});

		if (items.length !== reorderedItems.length) {
			throw new Error("Alguns itens não foram encontrados no ranking");
		}

		const updatePromises = reorderedItems.map(({ id, position }) => {
			return this.rankingItemRepository.update(id, { position });
		});

		await Promise.all(updatePromises);
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<RankingItem>, filters: IRankingItemFilters): void {
		if (filters.workId) {
			queryBuilder.andWhere("rankingItem.workId = :workId", {
				workId: filters.workId,
			});
		}

		if (filters.minRating !== undefined) {
			queryBuilder.andWhere("rankingItem.rating >= :minRating", {
				minRating: filters.minRating,
			});
		}

		if (filters.maxRating !== undefined) {
			queryBuilder.andWhere("rankingItem.rating <= :maxRating", {
				maxRating: filters.maxRating,
			});
		}
	}
}
