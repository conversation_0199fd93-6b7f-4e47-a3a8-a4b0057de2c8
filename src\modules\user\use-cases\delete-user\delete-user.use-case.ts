import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";

@Injectable()
export class DeleteUserUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(id: number): Promise<void> {
		const user = await this.userRepository.findById(id);
		if (!user) {
			throw new ResourceNotFoundException("Usuário", id);
		}

		await this.userRepository.delete(id);
	}
}
