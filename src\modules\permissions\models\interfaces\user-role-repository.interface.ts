import { UserRole } from "../entities";
import { Permission } from "../enums";

/**
 * Interface para o repositório de user roles
 * Define os métodos para gerenciamento da relação usuário-role
 */
export interface IUserRoleRepository {
	/**
	 * Busca uma relação user-role pelo ID
	 */
	findById(id: string): Promise<UserRole | null>;

	/**
	 * Lista todos os roles de um usuário
	 */
	findByUserId(userId: number): Promise<UserRole[]>;

	/**
	 * Lista todos os usuários com um role específico
	 */
	findByRoleId(roleId: string): Promise<UserRole[]>;

	/**
	 * Busca uma relação específica usuário-role
	 */
	findByUserAndRole(userId: number, roleId: string): Promise<UserRole | null>;

	/**
	 * Lista roles de um usuário com suas permissões
	 */
	findUserRolesWithPermissions(userId: number): Promise<UserRole[]>;

	/**
	 * Atribui um role a um usuário
	 */
	assignRole(userId: number, roleId: string, assignedBy?: number, expiresAt?: Date): Promise<UserRole>;

	/**
	 * Remove um role de um usuário
	 */
	removeRole(userId: number, roleId: string): Promise<void>;

	/**
	 * Remove todos os roles de um usuário
	 */
	removeAllUserRoles(userId: number): Promise<void>;

	/**
	 * Verifica se um usuário tem um role específico
	 */
	hasRole(userId: number, roleId: string): Promise<boolean>;

	/**
	 * Verifica se um usuário tem uma permissão específica
	 */
	hasPermission(userId: number, permission: Permission): Promise<boolean>;

	/**
	 * Lista todas as permissões de um usuário (através de seus roles)
	 */
	getUserPermissions(userId: number): Promise<Permission[]>;

	/**
	 * Atualiza uma relação user-role
	 */
	update(id: string, data: Partial<UserRole>): Promise<UserRole>;

	/**
	 * Lista roles expirados
	 */
	findExpiredRoles(): Promise<UserRole[]>;

	/**
	 * Remove roles expirados
	 */
	removeExpiredRoles(): Promise<void>;
}
