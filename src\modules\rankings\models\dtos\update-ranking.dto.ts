import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean, MaxLength } from "class-validator";

export class UpdateRankingDto {
	@ApiProperty({
		description: "Nome do ranking",
		example: "Top 10 Manhwas de Ação e Aventura",
		maxLength: 100,
		required: false,
	})
	@IsString()
	@IsOptional()
	@MaxLength(100)
	name?: string;

	@ApiProperty({
		description: "Descrição do ranking",
		example: "Minha seleção pessoal dos melhores manhwas de ação e aventura",
		required: false,
	})
	@IsString()
	@IsOptional()
	description?: string;

	@ApiProperty({
		description: "Definir se o ranking é público",
		example: true,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	isPublic?: boolean;

	@ApiProperty({
		description: "URL da imagem de capa do ranking",
		example: "https://example.com/images/rankings/action-adventure.jpg",
		required: false,
	})
	@IsString()
	@IsOptional()
	@MaxLength(255)
	coverImage?: string;
}
