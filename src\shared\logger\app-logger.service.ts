import { Injectable, Logger } from "@nestjs/common";

@Injectable()
export class App<PERSON>ogger extends Logger {
	private static instance: AppLogger;

	static getInstance(context?: string): AppLogger {
		if (!AppLogger.instance) {
			AppLogger.instance = new AppLogger(context);
		}
		return AppLogger.instance;
	}

	logBusinessOperation<T = unknown>(operation: string, data: T, userId?: string | number, context?: string): void {
		const logData = {
			operation,
			userId,
			data: this.sanitizeData(data),
			timestamp: new Date().toISOString(),
		};

		this.log(`Business Operation: ${operation}`, {
			context: context || this.context,
			...logData,
		});
	}

	logError(error: Error | string, trace?: string, context?: string, metadata?: Record<string, unknown>): void {
		this.error(typeof error === "string" ? error : error.message, trace || (typeof error === "object" ? error.stack : undefined), context || this.context);
	}

	logDatabaseOperation<T = unknown>(operation: string, table: string, conditions?: T, duration?: number): void {
		const logData = {
			operation,
			table,
			conditions: this.sanitizeData(conditions),
			duration: duration ? `${duration}ms` : undefined,
			timestamp: new Date().toISOString(),
		};

		this.debug(`DB Operation: ${operation} on ${table}`, {
			context: "DatabaseOperation",
			...logData,
		});
	}

	private sanitizeData<T>(data: T): T {
		if (!data) return data;

		const sensitiveFields = ["password", "token", "secret", "key", "authorization"];

		if (typeof data === "object" && data !== null) {
			const sanitized = { ...(data as Record<string, unknown>) };

			for (const field of sensitiveFields) {
				if (field in sanitized) {
					sanitized[field] = "***HIDDEN***";
				}
			}

			return sanitized as T;
		}

		return data;
	}
}
