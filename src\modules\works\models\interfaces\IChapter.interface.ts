export interface IChapter {
	id: string;
	workId: string;
	number: number;
	title?: string;
	releaseDate?: Date;
	url?: string;
	createdAt: Date;
}

export interface ICreateChapterRequest {
	workId: string;
	number: number;
	title?: string;
	releaseDate?: Date;
	url?: string;
}

export interface IUpdateChapterRequest {
	number?: number;
	title?: string;
	releaseDate?: Date;
	url?: string;
}

export interface IChapterRepository {
	create(data: ICreateChapterRequest): Promise<IChapter>;
	findById(id: string): Promise<IChapter | null>;
	findByWorkId(workId: string): Promise<IChapter[]>;
	findByWorkIdAndNumber(workId: string, number: number): Promise<IChapter | null>;
	update(id: string, data: IUpdateChapterRequest): Promise<IChapter>;
	delete(id: string): Promise<void>;
	getLatestChapterNumber(workId: string): Promise<number>;
}
