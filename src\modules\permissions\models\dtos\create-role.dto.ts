import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsOptional, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from "class-validator";

/**
 * DTO para criação de um novo role
 */
export class CreateRoleDto {
	@ApiProperty({
		description: "Nome único do role",
		example: "content_moderator",
		minLength: 3,
		maxLength: 50,
		examples: {
			moderator: {
				summary: "Moderador de Conteúdo",
				value: "content_moderator",
			},
			editor: {
				summary: "Editor de Obras",
				value: "works_editor",
			},
			reviewer: {
				summary: "Revisor",
				value: "content_reviewer",
			},
		},
	})
	@IsNotEmpty({ message: "Nome do role é obrigatório" })
	@IsString({ message: "Nome deve ser uma string" })
	@MinLength(3, { message: "Nome deve ter pelo menos 3 caracteres" })
	@MaxLength(50, { message: "Nome deve ter no máximo 50 caracteres" })
	name: string;

	@ApiProperty({
		description: "Des<PERSON><PERSON><PERSON> do role",
		example: "Moderador responsável por revisar e aprovar conteúdo",
		maxLength: 255,
		required: false,
		examples: {
			detailed: {
				summary: "Descrição Detalhada",
				value: "Moderador com permissões para revisar, aprovar e remover conteúdo inadequado",
			},
			simple: {
				summary: "Descrição Simples",
				value: "Moderador de conteúdo",
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Descrição deve ser uma string" })
	@MaxLength(255, { message: "Descrição deve ter no máximo 255 caracteres" })
	description?: string;

	@ApiProperty({
		description: "Se o role está ativo",
		example: true,
		default: true,
		required: false,
	})
	@IsOptional()
	@IsBoolean({ message: "isActive deve ser um valor booleano" })
	isActive?: boolean;

	@ApiProperty({
		description: "IDs das permissões a serem atribuídas ao role",
		example: ["uuid-1", "uuid-2", "uuid-3"],
		type: [String],
		required: false,
		examples: {
			basic: {
				summary: "Permissões Básicas",
				value: ["perm-read-works", "perm-create-reviews"],
			},
			moderator: {
				summary: "Permissões de Moderador",
				value: ["perm-moderate-content", "perm-manage-tags", "perm-suspend-users"],
			},
		},
	})
	@IsOptional()
	@IsArray({ message: "Permissões devem ser um array" })
	@IsUUID(4, { each: true, message: "Cada permissão deve ser um UUID válido" })
	permissionIds?: string[];
}
