import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from "typeorm";
import { Tag } from "./tag.entity";

@Entity("work_tags")
export class WorkTag {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	workId: string;

	@Column({ type: "uuid" })
	tagId: string;

	@ManyToOne(() => Tag, tag => tag.workTags, { onDelete: "CASCADE" })
	@JoinColumn({ name: "tagId" })
	tag: Tag;

	@CreateDateColumn()
	createdAt: Date;
}
