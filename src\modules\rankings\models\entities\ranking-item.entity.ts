import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from "typeorm";
import { Ranking } from "./ranking.entity";

@Entity("ranking_items")
export class RankingItem {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	rankingId: string;

	@Column({ type: "uuid" })
	workId: string;

	@Column({ type: "integer" })
	position: number;

	@Column({ type: "text", nullable: true })
	comment?: string;

	@Column({ type: "integer", nullable: true })
	rating?: number;

	@ManyToOne(() => Ranking, ranking => ranking.items, { onDelete: "CASCADE" })
	@JoinColumn({ name: "rankingId" })
	ranking: Ranking;

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
