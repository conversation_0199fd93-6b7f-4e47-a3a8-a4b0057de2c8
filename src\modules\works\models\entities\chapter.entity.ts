import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { Work } from "./work.entity";

@Entity("chapters")
@Index(["workId", "number"], { unique: true })
export class Chapter {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	workId: string;

	@Column({ type: "integer" })
	number: number;

	@Column({ type: "varchar", length: 255, nullable: true })
	title?: string;

	@Column({ type: "date", nullable: true })
	releaseDate?: Date;

	@Column({ type: "varchar", length: 500, nullable: true })
	url?: string;

	@ManyToOne(() => Work, work => work.chapters, {
		onDelete: "CASCADE",
	})
	@JoinColumn({ name: "workId" })
	work: Work;

	@CreateDateColumn()
	createdAt: Date;
}
