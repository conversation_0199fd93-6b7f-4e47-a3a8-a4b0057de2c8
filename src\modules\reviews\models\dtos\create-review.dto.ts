import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsUUID, IsNumber, IsOptional, IsBoolean, Min, Max, <PERSON>eng<PERSON> } from "class-validator";

export class CreateReviewDto {
	@ApiProperty({
		description: "ID da obra a ser avaliada",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
	})
	@IsUUID()
	@IsNotEmpty()
	workId: string;

	@ApiProperty({
		description: "Nota atribuída à obra (de 0 a 10)",
		example: 8.5,
		minimum: 0,
		maximum: 10,
	})
	@IsNumber()
	@IsNotEmpty()
	@Min(0)
	@Max(10)
	rating: number;

	@ApiProperty({
		description: "Título da review (opcional)",
		example: "Uma obra-prima do gênero!",
		required: false,
		maxLength: 100,
	})
	@IsString()
	@IsOptional()
	@MaxLength(100)
	title?: string;

	@ApiProperty({
		description: "Conteúdo da review",
		example: "Essa série tem uma trama envolvente e personagens bem desenvolvidos...",
	})
	@IsString()
	@IsNotEmpty()
	content: string;

	@ApiProperty({
		description: "Se a review é pública ou privada",
		example: true,
		default: true,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	isPublic?: boolean;

	@ApiProperty({
		description: "Se a review contém spoilers",
		example: false,
		default: false,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	hasContainsSpoilers?: boolean;
}
