import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { ListItem } from "../models/entities";
import { IListItemRepository, ICreateListItemRequest, IUpdateListItemRequest, IListItemFilters, IListItem } from "../models/interfaces";

@Injectable()
export class ListItemRepository implements IListItemRepository {
	constructor(
		@InjectRepository(ListItem)
		private readonly listItemRepository: Repository<ListItem>
	) {}

	async create(data: ICreateListItemRequest): Promise<IListItem> {
		let order = data.order;

		// Se não foi especificado uma ordem, usar a próxima disponível
		if (order === undefined) {
			order = await this.getNextOrder(data.listId);
		}

		const listItem = this.listItemRepository.create({
			...data,
			order,
		});

		return await this.listItemRepository.save(listItem);
	}

	async findById(id: string): Promise<IListItem | null> {
		return await this.listItemRepository.findOne({
			where: { id },
			relations: ["list", "work"],
		});
	}

	async findByListAndWork(listId: string, workId: string): Promise<IListItem | null> {
		return await this.listItemRepository.findOne({
			where: { listId, workId },
			relations: ["list", "work"],
		});
	}

	async findByList(filters: IListItemFilters): Promise<{ items: IListItem[]; total: number }> {
		const queryBuilder = this.listItemRepository
			.createQueryBuilder("listItem")
			.leftJoinAndSelect("listItem.work", "work")
			.leftJoinAndSelect("listItem.list", "list");

		this.applyFilters(queryBuilder, filters);
		this.applySorting(queryBuilder, filters);

		const total = await queryBuilder.getCount();

		const { page = 1, limit = 20 } = filters;
		const skip = (page - 1) * limit;

		queryBuilder.skip(skip).take(limit);

		const items = await queryBuilder.getMany();

		return { items, total };
	}

	async update(id: string, data: IUpdateListItemRequest): Promise<IListItem> {
		await this.listItemRepository.update(id, data);
		const updatedItem = await this.findById(id);
		if (!updatedItem) {
			throw new Error("ListItem not found after update");
		}
		return updatedItem;
	}

	async delete(id: string): Promise<void> {
		await this.listItemRepository.delete(id);
	}

	async getNextOrder(listId: string): Promise<number> {
		const result = await this.listItemRepository
			.createQueryBuilder("listItem")
			.select("MAX(listItem.order)", "maxOrder")
			.where("listItem.listId = :listId", { listId })
			.getRawOne();

		return (result?.maxOrder || 0) + 1;
	}

	async reorderItems(listId: string, itemOrders: { id: string; order: number }[]): Promise<void> {
		const queryRunner = this.listItemRepository.manager.connection.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			for (const { id, order } of itemOrders) {
				await queryRunner.manager.update(
					ListItem,
					{ id, listId }, // Garantir que o item pertence à lista
					{ order }
				);
			}

			await queryRunner.commitTransaction();
		} catch (error) {
			await queryRunner.rollbackTransaction();
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<ListItem>, filters: IListItemFilters): void {
		queryBuilder.where("listItem.listId = :listId", { listId: filters.listId });
	}

	private applySorting(queryBuilder: SelectQueryBuilder<ListItem>, filters: IListItemFilters): void {
		const { sortBy = "order", sortOrder = "ASC" } = filters;
		queryBuilder.orderBy(`listItem.${sortBy}`, sortOrder);
	}
}
