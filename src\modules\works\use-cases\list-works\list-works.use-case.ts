import { Injectable } from "@nestjs/common";
import { WorkRepository } from "../../repositories";
import { IWorkFilters, IWork } from "../../models/interfaces";

export interface IListWorksResponse {
	works: IWork[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

@Injectable()
export class ListWorksUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(filters: IWorkFilters): Promise<IListWorksResponse> {
		const { works, total } = await this.workRepository.findAll(filters);

		const page = filters.page || 1;
		const limit = filters.limit || 20;
		const totalPages = Math.ceil(total / limit);

		return {
			works,
			total,
			page,
			limit,
			totalPages,
		};
	}
}
