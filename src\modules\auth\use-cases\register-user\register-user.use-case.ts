import { Inject, Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcryptjs";
import { DatabaseException, DuplicateResourceException } from "../../../../shared/exceptions/business.exceptions";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";
import { User } from "../../../user/models/entities/user.entity";
import { AuthResponseDto } from "../../models/dtos/auth-response.dto";
import { RegisterDto } from "../../models/dtos/register.dto";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";

@Injectable()
export class RegisterUserUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository,
		private readonly jwtService: JwtService
	) {}

	async execute(registerDto: RegisterDto): Promise<AuthResponseDto> {
		try {
			// Verificar se email já existe
			const existingUser = await this.authRepository.findByUsernameOrEmail(registerDto.email);
			if (existingUser) {
				throw new DuplicateResourceException("Usuário", "email", registerDto.email);
			}

			// Verificar se username já existe
			const existingUsername = await this.authRepository.findByUsernameOrEmail(registerDto.username);
			if (existingUsername) {
				throw new DuplicateResourceException("Usuário", "username", registerDto.username);
			}

			// Hash da senha
			const hashedPassword = await bcrypt.hash(registerDto.password, 10);

			// Criar usuário
			const userData = {
				...registerDto,
				password: hashedPassword,
			};

			const user = await this.authRepository.create(userData);
			const { password, ...userWithoutPassword } = user;

			// Gerar tokens
			const accessTokenPayload = { username: user.username, sub: user.id, email: user.email };
			const refreshTokenPayload = { username: user.username, sub: user.id, type: "refresh" };

			const access_token = this.jwtService.sign(accessTokenPayload);
			const refresh_token = this.jwtService.sign(refreshTokenPayload, { expiresIn: "30d" });

			// Salvar refresh token no banco
			await this.authRepository.updateRefreshToken(user.id, refresh_token);

			return {
				access_token,
				refresh_token,
				token_type: "Bearer",
				expires_in: 604800, // 7 dias
				user: this.mapToUserResponseDto(userWithoutPassword),
			};
		} catch (error) {
			if (error instanceof DuplicateResourceException) {
				throw error;
			}
			throw new DatabaseException("criação de usuário", error);
		}
	}

	private mapToUserResponseDto(user: Partial<User>): UserResponseDto {
		return {
			id: user.id,
			username: user.username,
			email: user.email,
			fullName: user.fullName,
			avatar: user.avatar,
			isActive: user.isActive,
			role: user.role,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
		};
	}
}
