import { IRanking } from "./IRanking.interface";

export interface IRankingFilters {
	userId?: string;
	isPublic?: boolean;
	search?: string;
}

export interface IRankingRepository {
	create(ranking: Partial<IRanking>): Promise<IRanking>;
	update(id: string, ranking: Partial<IRanking>): Promise<IRanking>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<IRanking>;
	findByIdWithItems(id: string): Promise<IRanking>;
	findByUserId(userId: string, filters?: IRankingFilters): Promise<IRanking[]>;
	findPublicRankings(filters?: IRankingFilters): Promise<IRanking[]>;
	incrementItemsCount(id: string): Promise<void>;
	decrementItemsCount(id: string): Promise<void>;
}
