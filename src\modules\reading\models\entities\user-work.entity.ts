import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index, BeforeUpdate } from "typeorm";
import { ReadingStatus } from "../enums";
import { Work } from "../../../works/models/entities";

@Entity("user_works")
@Index(["userId", "workId"], { unique: true })
export class UserWork {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	userId: string;

	@Column({ type: "uuid" })
	workId: string;

	@Column({
		type: "enum",
		enum: ReadingStatus,
		default: ReadingStatus.PLAN_TO_READ,
	})
	status: ReadingStatus;

	@Column({ type: "integer", default: 0 })
	currentChapter: number;

	@Column({ type: "decimal", precision: 3, scale: 2, nullable: true })
	personalRating?: number;

	@Column({ type: "timestamp", nullable: true })
	startedAt?: Date;

	@Column({ type: "timestamp", nullable: true })
	completedAt?: Date;

	@Column({ type: "timestamp", nullable: true })
	lastReadAt?: Date;

	@ManyToOne(() => Work, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;

	@BeforeUpdate()
	updateTimestamps() {
		if (this.status === ReadingStatus.READING && !this.startedAt) {
			this.startedAt = new Date();
		}

		if (this.status === ReadingStatus.COMPLETED && !this.completedAt) {
			this.completedAt = new Date();
		}

		if (this.status === ReadingStatus.READING || this.currentChapter > 0) {
			this.lastReadAt = new Date();
		}
	}
}
