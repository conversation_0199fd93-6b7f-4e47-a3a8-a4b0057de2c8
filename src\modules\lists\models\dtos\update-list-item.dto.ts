import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsInt, Min, MaxLength } from "class-validator";

export class UpdateListItemDto {
	@ApiPropertyOptional({
		description: "Nota pessoal sobre a obra na lista",
		example: "Obra ainda melhor na segunda leitura!",
	})
	@IsOptional()
	@IsString()
	@MaxLength(500)
	note?: string;

	@ApiPropertyOptional({
		description: "Nova posição da obra na lista",
		example: 5,
		minimum: 0,
	})
	@IsOptional()
	@IsInt()
	@Min(0)
	order?: number;
}
