import { Inject, Injectable } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { DuplicateResourceException } from "../../../../shared/exceptions/business.exceptions";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { CreateUserDto } from "../../models/dtos/create-user.dto";
import { UserResponseDto } from "../../models/dtos/user-response.dto";
import { User } from "../../models/entities/user.entity";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";

@Injectable()
export class CreateUserUseCase extends BaseUseCase<CreateUserDto, UserResponseDto> {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {
		super("CreateUserUseCase");
	}

	async execute(createUserDto: CreateUserDto): Promise<UserResponseDto> {
		return this.executeWithLogging("CreateUser", createUserDto, async () => {
			// Verificar se email já existe
			const existingEmail = await this.userRepository.findByEmail(createUserDto.email);
			if (existingEmail) {
				throw new DuplicateResourceException("Usuário", "email", createUserDto.email);
			}

			// Verificar se username já existe
			const existingUsername = await this.userRepository.findByUsername(createUserDto.username);
			if (existingUsername) {
				throw new DuplicateResourceException("Usuário", "username", createUserDto.username);
			}

			// Hash da senha
			const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

			// Criar usuário
			const userData = {
				...createUserDto,
				password: hashedPassword,
			};

			const user = await this.userRepository.create(userData);
			return this.mapToResponseDto(user);
		});
	}

	private mapToResponseDto(user: User): UserResponseDto {
		const { password, ...userWithoutPassword } = user;
		return userWithoutPassword as UserResponseDto;
	}
}
