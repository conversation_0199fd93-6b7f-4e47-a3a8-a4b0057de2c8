import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { List, ListItem } from "./models/entities";
import { ListRepository, ListItemRepository } from "./repositories";
import {
	CreateListUseCase,
	UpdateListUseCase,
	DeleteListUseCase,
	GetListUseCase,
	ListUserListsUseCase,
	AddItemToListUseCase,
	RemoveItemFromListUseCase,
	GetListItemsUseCase,
} from "./use-cases";
import { ListsController } from "./controllers";
import { WorksModule } from "../works/works.module";

@Module({
	imports: [
		TypeOrmModule.forFeature([List, ListItem]),
		forwardRef(() => WorksModule), // Para ter acesso ao WorkRepository
	],
	controllers: [ListsController],
	providers: [
		ListRepository,
		ListItemRepository,
		CreateListUseCase,
		UpdateListUseCase,
		DeleteListUseCase,
		GetListUseCase,
		ListUserListsUseCase,
		AddItemToListUseCase,
		RemoveItemFromListUseCase,
		GetListItemsUseCase,
		{
			provide: "IListRepository",
			useExisting: ListRepository,
		},
		{
			provide: "IListItemRepository",
			useExisting: ListItemRepository,
		},
	],
	exports: [
		"IListRepository",
		"IListItemRepository",
		ListRepository,
		ListItemRepository,
		CreateListUseCase,
		UpdateListUseCase,
		DeleteListUseCase,
		GetListUseCase,
		ListUserListsUseCase,
		AddItemToListUseCase,
		RemoveItemFromListUseCase,
		GetListItemsUseCase,
	],
})
export class ListsModule {}
