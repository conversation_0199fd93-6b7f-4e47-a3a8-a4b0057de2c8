import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { Tag } from "../models/entities/tag.entity";
import { ITag, ITagFilters, ITagRepository } from "../models/interfaces";

@Injectable()
export class TagRepository implements ITagRepository {
	constructor(
		@InjectRepository(Tag)
		private readonly tagRepository: Repository<Tag>
	) {}

	async create(tag: Partial<ITag>): Promise<ITag> {
		const newTag = this.tagRepository.create(tag);
		return await this.tagRepository.save(newTag);
	}

	async update(id: string, tag: Partial<ITag>): Promise<ITag> {
		await this.tagRepository.update(id, tag);
		return await this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.tagRepository.delete(id);
	}

	async findById(id: string): Promise<ITag> {
		return await this.tagRepository.findOne({
			where: { id },
		});
	}

	async findByName(name: string): Promise<ITag> {
		return await this.tagRepository.findOne({
			where: { name },
		});
	}

	async findAll(filters?: ITagFilters): Promise<ITag[]> {
		const queryBuilder = this.tagRepository.createQueryBuilder("tag");

		if (filters) {
			this.applyFilters(queryBuilder, filters);
		}

		// Definir ordenação padrão
		const sortBy = filters?.sortBy || "name";
		const sortOrder = filters?.sortOrder || "ASC";

		queryBuilder.orderBy(`tag.${sortBy}`, sortOrder);

		return await queryBuilder.getMany();
	}

	async findPopularTags(limit: number = 10): Promise<ITag[]> {
		return await this.tagRepository.find({
			order: {
				worksCount: "DESC",
			},
			take: limit,
		});
	}

	async incrementWorksCount(id: string): Promise<void> {
		await this.tagRepository.increment({ id }, "worksCount", 1);
	}

	async decrementWorksCount(id: string): Promise<void> {
		await this.tagRepository.decrement({ id }, "worksCount", 1);
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<Tag>, filters: ITagFilters): void {
		if (filters.search) {
			queryBuilder.andWhere("(LOWER(tag.name) LIKE LOWER(:search) OR LOWER(tag.description) LIKE LOWER(:search))", {
				search: `%${filters.search}%`,
			});
		}
	}
}
