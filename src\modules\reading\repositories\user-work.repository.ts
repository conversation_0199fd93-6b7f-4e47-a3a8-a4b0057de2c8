import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { UserWork } from '../models/entities';
import {
	IUserWorkRepository,
	ICreateUserWorkRequest,
	IUpdateUserWorkRequest,
	IUserWorkFilters,
	IUserWork,
} from '../models/interfaces';
import { ReadingStatus } from '../models/enums';

@Injectable()
export class UserWorkRepository implements IUserWorkRepository {
	constructor(
		@InjectRepository(UserWork)
		private readonly userWorkRepository: Repository<UserWork>,
	) {}

	async create(data: ICreateUserWorkRequest): Promise<IUserWork> {
		const userWork = this.userWorkRepository.create(data);
		
		// Definir timestamps baseado no status
		if (data.status === ReadingStatus.READING) {
			userWork.startedAt = new Date();
			userWork.lastReadAt = new Date();
		} else if (data.status === ReadingStatus.COMPLETED) {
			userWork.startedAt = new Date();
			userWork.completedAt = new Date();
			userWork.lastReadAt = new Date();
		}

		return await this.userWorkRepository.save(userWork);
	}

	async findById(id: string): Promise<IUserWork | null> {
		return await this.userWorkRepository.findOne({
			where: { id },
			relations: ['work'],
		});
	}

	async findByUserAndWork(userId: string, workId: string): Promise<IUserWork | null> {
		return await this.userWorkRepository.findOne({
			where: { userId, workId },
			relations: ['work'],
		});
	}

	async findByUser(filters: IUserWorkFilters): Promise<{ userWorks: IUserWork[]; total: number }> {
		const queryBuilder = this.userWorkRepository
			.createQueryBuilder('userWork')
			.leftJoinAndSelect('userWork.work', 'work');

		this.applyFilters(queryBuilder, filters);
		this.applySorting(queryBuilder, filters);

		const total = await queryBuilder.getCount();

		const { page = 1, limit = 20 } = filters;
		const skip = (page - 1) * limit;

		queryBuilder.skip(skip).take(limit);

		const userWorks = await queryBuilder.getMany();

		return { userWorks, total };
	}

	async update(id: string, data: IUpdateUserWorkRequest): Promise<IUserWork> {
		await this.userWorkRepository.update(id, data);
		const updatedUserWork = await this.findById(id);
		if (!updatedUserWork) {
			throw new Error('UserWork not found after update');
		}
		return updatedUserWork;
	}

	async delete(id: string): Promise<void> {
		await this.userWorkRepository.delete(id);
	}

	async updateProgress(userId: string, workId: string, currentChapter: number): Promise<IUserWork> {
		const userWork = await this.findByUserAndWork(userId, workId);
		if (!userWork) {
			throw new Error('UserWork not found');
		}

		const updateData: IUpdateUserWorkRequest = {
			currentChapter,
		};

		// Se estava como "plan to read" e começou a ler, mudar para "reading"
		if (userWork.status === ReadingStatus.PLAN_TO_READ && currentChapter > 0) {
			updateData.status = ReadingStatus.READING;
		}

		return await this.update(userWork.id, updateData);
	}

	async getReadingStats(userId: string): Promise<{
		reading: number;
		completed: number;
		dropped: number;
		planToRead: number;
		onHold: number;
		totalWorks: number;
	}> {
		const stats = await this.userWorkRepository
			.createQueryBuilder('userWork')
			.select('userWork.status', 'status')
			.addSelect('COUNT(*)', 'count')
			.where('userWork.userId = :userId', { userId })
			.groupBy('userWork.status')
			.getRawMany();

		const result = {
			reading: 0,
			completed: 0,
			dropped: 0,
			planToRead: 0,
			onHold: 0,
			totalWorks: 0,
		};

		stats.forEach((stat) => {
			const count = parseInt(stat.count);
			result.totalWorks += count;

			switch (stat.status) {
				case ReadingStatus.READING:
					result.reading = count;
					break;
				case ReadingStatus.COMPLETED:
					result.completed = count;
					break;
				case ReadingStatus.DROPPED:
					result.dropped = count;
					break;
				case ReadingStatus.PLAN_TO_READ:
					result.planToRead = count;
					break;
				case ReadingStatus.ON_HOLD:
					result.onHold = count;
					break;
			}
		});

		return result;
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<UserWork>, filters: IUserWorkFilters): void {
		queryBuilder.where('userWork.userId = :userId', { userId: filters.userId });

		if (filters.status) {
			queryBuilder.andWhere('userWork.status = :status', { status: filters.status });
		}

		if (filters.workType) {
			queryBuilder.andWhere('work.type = :workType', { workType: filters.workType });
		}

		if (filters.minRating !== undefined) {
			queryBuilder.andWhere('userWork.personalRating >= :minRating', {
				minRating: filters.minRating,
			});
		}

		if (filters.maxRating !== undefined) {
			queryBuilder.andWhere('userWork.personalRating <= :maxRating', {
				maxRating: filters.maxRating,
			});
		}

		if (filters.search) {
			queryBuilder.andWhere('work.title ILIKE :search', {
				search: `%${filters.search}%`,
			});
		}
	}

	private applySorting(queryBuilder: SelectQueryBuilder<UserWork>, filters: IUserWorkFilters): void {
		const { sortBy = 'lastReadAt', sortOrder = 'DESC' } = filters;
		queryBuilder.orderBy(`userWork.${sortBy}`, sortOrder);
	}
}
