import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, In } from "typeorm";
import { PermissionEntity } from "../models/entities";
import { IPermissionRepository } from "../models/interfaces";
import { Permission } from "../models/enums";

@Injectable()
export class PermissionTypeOrmRepository implements IPermissionRepository {
	constructor(
		@InjectRepository(PermissionEntity)
		private readonly permissionRepository: Repository<PermissionEntity>
	) {}

	async findById(id: string): Promise<PermissionEntity | null> {
		return this.permissionRepository.findOne({ where: { id } });
	}

	async findByName(name: Permission): Promise<PermissionEntity | null> {
		return this.permissionRepository.findOne({ where: { name } });
	}

	async findAll(): Promise<PermissionEntity[]> {
		return this.permissionRepository.find({
			where: { isActive: true },
			order: { name: "ASC" },
		});
	}

	async findByResource(resource: string): Promise<PermissionEntity[]> {
		return this.permissionRepository.find({
			where: { resource, isActive: true },
			order: { name: "ASC" },
		});
	}

	async findByAction(action: string): Promise<PermissionEntity[]> {
		return this.permissionRepository.find({
			where: { action, isActive: true },
			order: { name: "ASC" },
		});
	}

	async findByIds(ids: string[]): Promise<PermissionEntity[]> {
		return this.permissionRepository.find({
			where: { id: In(ids), isActive: true },
		});
	}

	async findByNames(names: Permission[]): Promise<PermissionEntity[]> {
		return this.permissionRepository.find({
			where: { name: In(names), isActive: true },
		});
	}

	async create(permissionData: Partial<PermissionEntity>): Promise<PermissionEntity> {
		const permission = this.permissionRepository.create(permissionData);
		return this.permissionRepository.save(permission);
	}

	async update(id: string, permissionData: Partial<PermissionEntity>): Promise<PermissionEntity> {
		await this.permissionRepository.update(id, permissionData);
		return this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.permissionRepository.update(id, { isActive: false });
	}

	async exists(name: Permission): Promise<boolean> {
		const count = await this.permissionRepository.count({
			where: { name, isActive: true },
		});
		return count > 0;
	}
}
