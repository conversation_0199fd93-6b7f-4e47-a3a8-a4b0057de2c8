import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from "typeorm";
import { ListItem } from "./list-item.entity";

@Entity("lists")
@Index(["userId", "name"], { unique: true }) // Um usuário não pode ter duas listas com o mesmo nome
export class List {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	userId: string;

	@Column({ type: "varchar", length: 100 })
	name: string;

	@Column({ type: "text", nullable: true })
	description?: string;

	@Column({ type: "boolean", default: false })
	isPublic: boolean;

	@Column({ type: "varchar", length: 255, nullable: true })
	coverImage?: string;

	@Column({ type: "integer", default: 0 })
	itemsCount: number;

	@OneToMany(() => ListItem, listItem => listItem.list, { cascade: true })
	items: ListItem[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
