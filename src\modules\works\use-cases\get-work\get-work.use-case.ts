import { Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IWork } from "../../models/interfaces";
import { WorkRepository } from "../../repositories";

@Injectable()
export class GetWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(id: string): Promise<IWork> {
		const work = await this.workRepository.findById(id);
		if (!work) {
			throw new ResourceNotFoundException("Obra", id);
		}

		return work;
	}
}
