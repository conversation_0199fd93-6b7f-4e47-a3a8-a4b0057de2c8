export interface DatabaseConfig {
	host: string;
	port: number;
	username: string;
	password: string;
	database: string;
}

export interface JwtConfig {
	secret: string;
	expiresIn: string;
}

export interface AppConfig {
	port: number;
	nodeEnv: string;
	database: DatabaseConfig;
	jwt: JwtConfig;
}

export const getConfig = (): AppConfig => ({
	port: parseInt(process.env.PORT || "3000", 10),
	nodeEnv: process.env.NODE_ENV || "development",
	database: {
		host: process.env.DATABASE_HOST || "localhost",
		port: parseInt(process.env.DATABASE_PORT || "5432", 10),
		username: process.env.DATABASE_USERNAME || "postgres",
		password: process.env.DATABASE_PASSWORD || "postgres",
		database: process.env.DATABASE_NAME || "backend_test",
	},
	jwt: {
		secret: process.env.JWT_SECRET || "default-secret-change-in-production",
		expiresIn: process.env.JWT_EXPIRES_IN || "7d",
	},
});
