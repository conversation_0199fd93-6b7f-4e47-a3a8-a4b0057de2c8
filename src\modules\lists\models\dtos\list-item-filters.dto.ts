import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsInt, IsIn, Min } from "class-validator";
import { Transform } from "class-transformer";

export class ListItemFiltersDto {
	@ApiPropertyOptional({
		description: "Número da página",
		example: 1,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsInt()
	@Min(1)
	page?: number;

	@ApiPropertyOptional({
		description: "Itens por página",
		example: 20,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsInt()
	@Min(1)
	limit?: number;

	@ApiPropertyOptional({
		description: "Campo para ordenação",
		example: "order",
		enum: ["order", "createdAt"],
	})
	@IsOptional()
	@IsIn(["order", "createdAt"])
	sortBy?: "order" | "createdAt";

	@ApiPropertyOptional({
		description: "Direção da ordenação",
		example: "ASC",
		enum: ["ASC", "DESC"],
	})
	@IsOptional()
	@IsIn(["ASC", "DESC"])
	sortOrder?: "ASC" | "DESC";
}
