import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, Length } from "class-validator";

export class LoginDto {
	@ApiProperty({
		description: "Nome de usuário ou endereço de email para autenticação",
		example: "joao_silva",
		examples: {
			username: {
				summary: "Userna<PERSON>",
				value: "joao_silva",
			},
			email: {
				summary: "Email",
				value: "<EMAIL>",
			},
		},
		type: "string",
		format: "username-or-email",
	})
	@IsNotEmpty({ message: "Username ou email é obrigatório" })
	@IsString({ message: "Username ou email deve ser uma string" })
	@Length(3, 255, { message: "Username ou email deve ter entre 3 e 255 caracteres" })
	usernameOrEmail: string;

	@ApiProperty({
		description: "Senha do usuário para autenticação",
		example: "MinhaSenh@123",
		type: "string",
		format: "password",
		minLength: 6,
		maxLength: 128,
	})
	@IsNotEmpty({ message: "Senha é obrigatória" })
	@IsString({ message: "Senha deve ser uma string" })
	@Length(6, 128, { message: "Senha deve ter entre 6 e 128 caracteres" })
	password: string;
}
