import { Inject, Injectable } from "@nestjs/common";
import { IRanking, IRankingFilters, IRankingRepository } from "../../models/interfaces";
import { RankingFiltersDto } from "../../models/dtos";

@Injectable()
export class ListUserRankingsUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(userId: string, filters?: RankingFiltersDto): Promise<IRanking[]> {
		const rankingFilters: IRankingFilters = {
			...filters,
		};

		return await this.rankingRepository.findByUserId(userId, rankingFilters);
	}
}
