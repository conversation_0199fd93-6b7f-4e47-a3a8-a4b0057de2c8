import { IReviewComment, IReviewCommentFilters } from "./IReviewComment.interface";

export interface IReviewCommentRepository {
	create(comment: Partial<IReviewComment>): Promise<IReviewComment>;
	update(id: string, comment: Partial<IReviewComment>): Promise<IReviewComment>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<IReviewComment>;
	findAll(filters: IReviewCommentFilters): Promise<IReviewComment[]>;
	findCount(filters: IReviewCommentFilters): Promise<number>;
	incrementLikes(id: string): Promise<void>;
	decrementLikes(id: string): Promise<void>;
	incrementDislikes(id: string): Promise<void>;
	decrementDislikes(id: string): Promise<void>;
	findByReviewId(reviewId: string, includeReplies?: boolean): Promise<IReviewComment[]>;
	findRepliesByCommentId(commentId: string): Promise<IReviewComment[]>;
}
