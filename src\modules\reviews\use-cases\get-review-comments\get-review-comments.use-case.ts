import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { ReviewCommentFiltersDto } from "../../models/dtos";
import { IReviewComment, IReviewCommentRepository, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class GetReviewCommentsUseCase {
	constructor(
		@Inject("IReviewCommentRepository")
		private readonly commentRepository: IReviewCommentRepository,

		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(reviewId: string, filters: ReviewCommentFiltersDto, currentUserId?: string): Promise<{ data: IReviewComment[]; total: number }> {
		// Verificar se a review existe
		const review = await this.reviewRepository.findById(reviewId);
		if (!review) {
			throw new ResourceNotFoundException("Avaliação", reviewId);
		}
		// Se a review for privada, apenas o autor pode ver os comentários
		if (!review.isPublic && review.userId !== currentUserId) {
			throw new ResourceNotFoundException("Avaliação privada", `${reviewId}:${currentUserId}`);
		}

		// Adicionar o reviewId ao filtro
		filters.reviewId = reviewId;

		// Buscar comentários (apenas de primeiro nível, a menos que parentId seja especificado)
		if (filters.parentId === undefined) {
			filters.parentId = null;
		}

		const [comments, total] = await Promise.all([this.commentRepository.findAll(filters), this.commentRepository.findCount(filters)]);

		return { data: comments, total };
	}
}
