import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { ITagRepository, IWorkTagRepository } from "../../models/interfaces";

@Injectable()
export class RemoveTagFromWorkUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository,

		@Inject("IWorkTagRepository")
		private workTagRepository: IWorkTagRepository
	) {}

	async execute(workId: string, tagId: string): Promise<void> {
		// Verificar se a associação existe
		const workTag = await this.workTagRepository.findByWorkIdAndTagId(workId, tagId);

		if (!workTag) {
			throw new ResourceNotFoundException("Tag associada à obra", `${workId}:${tagId}`);
		}

		// Remover a associação
		await this.workTagRepository.deleteByWorkAndTag(workId, tagId);

		// Decrementar o contador de obras na tag
		await this.tagRepository.decrementWorksCount(tagId);
	}
}
