import { Inject, Injectable } from "@nestjs/common";
import { IWorkTagRepository } from "../../models/interfaces";

@Injectable()
export class GetWorksByTagUseCase {
	constructor(
		@Inject("IWorkTagRepository")
		private workTagRepository: IWorkTagRepository,

		@Inject("IWorkRepository")
		private workRepository: any // Usando any pois o IWorkRepository não foi importado
	) {}

	async execute(tagIds: string[]): Promise<any[]> {
		if (tagIds.length === 0) {
			return [];
		}

		// Buscar IDs de obras com as tags especificadas
		const workIds = await this.workTagRepository.findWorksByTags(tagIds);

		if (workIds.length === 0) {
			return [];
		}

		// Buscar os detalhes das obras encontradas
		return await this.workRepository.findByIds(workIds);
	}
}
