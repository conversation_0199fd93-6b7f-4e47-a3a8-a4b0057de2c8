import { PermissionEntity } from "../entities";
import { Permission } from "../enums";

/**
 * Interface para o repositório de permissões
 * Define os métodos para gerenciamento de permissões no sistema
 */
export interface IPermissionRepository {
	/**
	 * Busca uma permissão pelo ID
	 */
	findById(id: string): Promise<PermissionEntity | null>;

	/**
	 * Busca uma permissão pelo nome
	 */
	findByName(name: Permission): Promise<PermissionEntity | null>;

	/**
	 * Lista todas as permissões ativas
	 */
	findAll(): Promise<PermissionEntity[]>;

	/**
	 * Lista permissões por recurso
	 */
	findByResource(resource: string): Promise<PermissionEntity[]>;

	/**
	 * Lista permissões por ação
	 */
	findByAction(action: string): Promise<PermissionEntity[]>;

	/**
	 * Busca múltiplas permissões pelos IDs
	 */
	findByIds(ids: string[]): Promise<PermissionEntity[]>;

	/**
	 * Busca múltiplas permissões pelos nomes
	 */
	findByNames(names: Permission[]): Promise<PermissionEntity[]>;

	/**
	 * Cria uma nova permissão
	 */
	create(permissionData: Partial<PermissionEntity>): Promise<PermissionEntity>;

	/**
	 * Atualiza uma permissão existente
	 */
	update(id: string, permissionData: Partial<PermissionEntity>): Promise<PermissionEntity>;

	/**
	 * Remove uma permissão (soft delete)
	 */
	delete(id: string): Promise<void>;

	/**
	 * Verifica se uma permissão existe
	 */
	exists(name: Permission): Promise<boolean>;
}
