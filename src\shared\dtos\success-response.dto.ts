import { ApiProperty } from "@nestjs/swagger";

export class SuccessResponseDto<T = any> {
	@ApiProperty({
		description: "Indica se a operação foi bem-sucedida",
		example: true,
		type: "boolean",
	})
	success: boolean;

	@ApiProperty({
		description: "Mensagem de sucesso",
		example: "Operação realizada com sucesso",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "Dados retornados pela operação",
		required: false,
	})
	data?: T;

	@ApiProperty({
		description: "Metadados adicionais da resposta",
		example: {
			timestamp: "2024-06-12T10:30:00.000Z",
			version: "1.0.0",
			requestId: "req_123456789",
		},
		required: false,
	})
	meta?: {
		timestamp: string;
		version: string;
		requestId?: string;
		pagination?: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
		};
	};
}

export class PaginatedResponseDto<T = any> extends SuccessResponseDto<T[]> {
	@ApiProperty({
		description: "Lista de itens paginados",
		type: "array",
	})
	data: T[];

	@ApiProperty({
		description: "Informações de paginação",
		example: {
			page: 1,
			limit: 10,
			total: 100,
			totalPages: 10,
			hasNext: true,
			hasPrevious: false,
		},
	})
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
		hasNext: boolean;
		hasPrevious: boolean;
	};
}

export class DeleteSuccessResponseDto {
	@ApiProperty({
		description: "Indica se a exclusão foi bem-sucedida",
		example: true,
		type: "boolean",
	})
	success: boolean;

	@ApiProperty({
		description: "Mensagem de confirmação da exclusão",
		example: "Recurso deletado com sucesso",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "ID do recurso deletado",
		example: "123",
		type: "string",
	})
	deletedId: string;

	@ApiProperty({
		description: "Timestamp da exclusão",
		example: "2024-06-12T10:30:00.000Z",
		type: "string",
		format: "date-time",
	})
	deletedAt: string;
}
