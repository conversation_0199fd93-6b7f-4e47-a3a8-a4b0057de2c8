import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsUUID, IsOptional } from "class-validator";

export class CreateReviewCommentDto {
	@ApiProperty({
		description: "ID da review a ser comentada",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
	})
	@IsUUID()
	@IsNotEmpty()
	reviewId: string;

	@ApiProperty({
		description: "Conteúdo do comentário",
		example: "Concordo com sua análise! Também achei os personagens muito bem desenvolvidos.",
	})
	@IsString()
	@IsNotEmpty()
	content: string;

	@ApiProperty({
		description: "ID do comentário pai (se for uma resposta a outro comentário)",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
		required: false,
	})
	@IsUUID()
	@IsOptional()
	parentId?: string;
}
