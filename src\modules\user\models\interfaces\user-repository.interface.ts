import { User } from "../entities/user.entity";

export interface IUserRepository {
	findByEmail(email: string): Promise<User | null>;
	findByUsername(username: string): Promise<User | null>;
	findByUsernameOrEmail(usernameOrEmail: string): Promise<User | null>;
	findById(id: number): Promise<User | null>;
	create(userData: Partial<User>): Promise<User>;
	update(id: number, userData: Partial<User>): Promise<User>;
	delete(id: number): Promise<void>;
	findAll(): Promise<User[]>;
}
