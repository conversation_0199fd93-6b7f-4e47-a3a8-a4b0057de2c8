import { Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IUpdateWorkRequest, IWork } from "../../models/interfaces";
import { WorkRepository } from "../../repositories";

@Injectable()
export class UpdateWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(id: string, data: IUpdateWorkRequest): Promise<IWork> {
		// Verificar se a obra existe
		const existingWork = await this.workRepository.findById(id);
		if (!existingWork) {
			throw new ResourceNotFoundException("Obra", id);
		}

		// Se está tentando alterar o título, verificar se não existe outra obra com o mesmo título
		if (data.title && data.title !== existingWork.title) {
			const workWithSameTitle = await this.workRepository.findByTitle(data.title);
			if (workWithSameTitle) {
				throw new DuplicateResourceException("Obra", "title", data.title);
			}
		}

		return await this.workRepository.update(id, data);
	}
}
