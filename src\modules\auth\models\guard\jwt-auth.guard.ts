import { ExecutionContext, Injectable } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { AuthenticationException, InvalidTokenException } from "../../../../shared/exceptions/business.exceptions";

@Injectable()
export class JwtAuthGuard extends AuthGuard("jwt") {
	canActivate(context: ExecutionContext) {
		return super.canActivate(context);
	}

	handleRequest(err: any, user: any, info: any) {
		if (err || !user) {
			if (info?.name === "TokenExpiredError") {
				throw new InvalidTokenException("access", "Token expirado");
			} else if (info?.name === "JsonWebTokenError") {
				throw new InvalidTokenException("access", "Token malformado");
			} else if (info?.name === "NotBeforeError") {
				throw new InvalidTokenException("access", "Token ainda não é válido");
			} else if (info?.message === "No auth token") {
				throw new AuthenticationException("Token de acesso não fornecido");
			} else {
				throw new AuthenticationException("Token de acesso inválido ou ausente");
			}
		}

		return user;
	}
}
