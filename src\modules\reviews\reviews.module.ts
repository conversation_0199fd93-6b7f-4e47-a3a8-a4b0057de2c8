import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Review, ReviewComment, ReviewReaction, CommentReaction } from "./models/entities";
import { ReviewRepository, ReviewCommentRepository, ReviewReactionRepository, CommentReactionRepository } from "./repositories";
import {
	CreateReviewUseCase,
	UpdateReviewUseCase,
	DeleteReviewUseCase,
	GetReviewUseCase,
	ListReviewsUseCase,
	CreateReviewCommentUseCase,
	UpdateReviewCommentUseCase,
	DeleteReviewCommentUseCase,
	GetReviewCommentsUseCase,
	CreateReviewReactionUseCase,
	DeleteReviewReactionUseCase,
	CreateCommentReactionUseCase,
	DeleteCommentReactionUseCase,
} from "./use-cases";
import { ReviewsController } from "./controllers";
import { WorksModule } from "../works/works.module";

@Module({
	imports: [
		TypeOrmModule.forFeature([Review, ReviewComment, ReviewReaction, CommentReaction]),
		forwardRef(() => WorksModule), // Para evitar dependência circular
	],
	controllers: [ReviewsController],
	providers: [
		ReviewRepository,
		ReviewCommentRepository,
		ReviewReactionRepository,
		CommentReactionRepository,
		CreateReviewUseCase,
		UpdateReviewUseCase,
		DeleteReviewUseCase,
		GetReviewUseCase,
		ListReviewsUseCase,
		CreateReviewCommentUseCase,
		UpdateReviewCommentUseCase,
		DeleteReviewCommentUseCase,
		GetReviewCommentsUseCase,
		CreateReviewReactionUseCase,
		DeleteReviewReactionUseCase,
		CreateCommentReactionUseCase,
		DeleteCommentReactionUseCase,
		{
			provide: "IReviewRepository",
			useExisting: ReviewRepository,
		},
		{
			provide: "IReviewCommentRepository",
			useExisting: ReviewCommentRepository,
		},
		{
			provide: "IReviewReactionRepository",
			useExisting: ReviewReactionRepository,
		},
		{
			provide: "ICommentReactionRepository",
			useExisting: CommentReactionRepository,
		},
	],
	exports: [
		"IReviewRepository",
		"IReviewCommentRepository",
		"IReviewReactionRepository",
		"ICommentReactionRepository",
		ReviewRepository,
		ReviewCommentRepository,
		ReviewReactionRepository,
		CommentReactionRepository,
		CreateReviewUseCase,
		UpdateReviewUseCase,
		DeleteReviewUseCase,
		GetReviewUseCase,
		ListReviewsUseCase,
		CreateReviewCommentUseCase,
		UpdateReviewCommentUseCase,
		DeleteReviewCommentUseCase,
		GetReviewCommentsUseCase,
		CreateReviewReactionUseCase,
		DeleteReviewReactionUseCase,
		CreateCommentReactionUseCase,
		DeleteCommentReactionUseCase,
	],
})
export class ReviewsModule {}
