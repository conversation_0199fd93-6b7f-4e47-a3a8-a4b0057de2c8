import { IReviewComment } from "./IReviewComment.interface";
import { IReviewReaction } from "./IReviewReaction.interface";

export interface IReview {
	id: string;
	userId: string;
	workId: string;
	rating: number;
	title?: string;
	content: string;
	likes: number;
	dislikes: number;
	isPublic: boolean;
	hasContainsSpoilers: boolean;
	comments?: IReviewComment[];
	reactions?: IReviewReaction[];
	createdAt: Date;
	updatedAt: Date;
}

export interface IReviewFilters {
	userId?: string;
	workId?: string;
	isPublic?: boolean;
	minRating?: number;
	maxRating?: number;
	skip?: number;
	take?: number;
	sort?: string;
	order?: "ASC" | "DESC";
}
