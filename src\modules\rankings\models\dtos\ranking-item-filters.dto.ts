import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsUUID, IsString, IsInt, Min, Max } from "class-validator";
import { Transform } from "class-transformer";

export class RankingItemFiltersDto {
	@ApiPropertyOptional({
		description: "Filtrar por ID da obra",
		example: "123e4567-e89b-12d3-a456-************",
		required: false,
	})
	@IsOptional()
	@IsUUID()
	workId?: string;

	@ApiPropertyOptional({
		description: "Nota mínima (1-10)",
		example: 7,
		required: false,
		minimum: 1,
		maximum: 10,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsInt()
	@Min(1)
	@Max(10)
	minRating?: number;

	@ApiPropertyOptional({
		description: "Nota máxima (1-10)",
		example: 10,
		required: false,
		minimum: 1,
		maximum: 10,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsInt()
	@Min(1)
	@Max(10)
	maxRating?: number;

	@ApiPropertyOptional({
		description: "Ordenar por (position, rating, createdAt)",
		example: "position",
		enum: ["position", "rating", "createdAt"],
		default: "position",
		required: false,
	})
	@IsOptional()
	@IsString()
	sortBy?: string = "position";

	@ApiPropertyOptional({
		description: "Ordem (ASC, DESC)",
		example: "ASC",
		enum: ["ASC", "DESC"],
		default: "ASC",
		required: false,
	})
	@IsOptional()
	@IsString()
	sortOrder?: string = "ASC";
}
