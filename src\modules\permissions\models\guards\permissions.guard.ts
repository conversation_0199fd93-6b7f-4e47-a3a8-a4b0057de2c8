import { Injectable, CanActivate, ExecutionContext, Inject } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { IUserRoleRepository } from "../interfaces";
import { Permission } from "../enums";
import { PERMISSIONS_KEY } from "../decorators";

/**
 * Guard que verifica se o usuário possui as permissões necessárias
 * Funciona em conjunto com o decorator @RequirePermissions()
 */
@Injectable()
export class PermissionsGuard implements CanActivate {
	constructor(
		private reflector: Reflector,
		@Inject("IUserRoleRepository")
		private readonly userRoleRepository: IUserRoleRepository
	) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		// Busca as permissões requeridas definidas no decorator @RequirePermissions()
		const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [context.getHandler(), context.getClass()]);

		// Se não há permissões requeridas, permite acesso
		if (!requiredPermissions || requiredPermissions.length === 0) {
			return true;
		}

		// Extrai o usuário da requisição
		const request = context.switchToHttp().getRequest();
		const user = request.user;

		// Se não há usuário autenticado, nega acesso
		if (!user || !user.id) {
			return false;
		}

		try {
			// Busca todas as permissões do usuário através de seus roles
			const userPermissions = await this.userRoleRepository.getUserPermissions(user.id);

			// Verifica se o usuário possui todas as permissões requeridas
			const hasAllPermissions = requiredPermissions.every(permission => userPermissions.includes(permission));

			return hasAllPermissions;
		} catch (error) {
			// Em caso de erro, nega acesso por segurança
			console.error("Erro ao verificar permissões do usuário:", error);
			return false;
		}
	}
}
