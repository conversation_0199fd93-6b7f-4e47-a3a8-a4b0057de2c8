import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, <PERSON> } from "class-validator";

export class RegisterDto {
	@ApiProperty({
		description: "Nome de usuário único no sistema",
		example: "joao_silva",
		minLength: 3,
		maxLength: 50,
		pattern: "^[a-zA-Z0-9_.-]+$",
		examples: {
			simple: {
				summary: "Username Simples",
				value: "joao_silva",
			},
			withNumbers: {
				summary: "Com Números",
				value: "user123",
			},
			withDots: {
				summary: "Com Pontos",
				value: "joao.silva",
			},
		},
	})
	@IsNotEmpty({ message: "Username é obrigatório" })
	@IsString({ message: "Username deve ser uma string" })
	@MinLength(3, { message: "Username deve ter pelo menos 3 caracteres" })
	@MaxLength(50, { message: "Username deve ter no máximo 50 caracteres" })
	@Matches(/^[a-zA-Z0-9_.-]+$/, { message: "Username deve conter apenas letras, números, _, . ou -" })
	username: string;

	@ApiProperty({
		description: "Endereço de email válido e único",
		example: "<EMAIL>",
		format: "email",
		examples: {
			personal: {
				summary: "Email Pessoal",
				value: "<EMAIL>",
			},
			corporate: {
				summary: "Email Corporativo",
				value: "<EMAIL>",
			},
		},
	})
	@IsNotEmpty({ message: "Email é obrigatório" })
	@IsEmail({}, { message: "Email deve ter um formato válido" })
	email: string;

	@ApiProperty({
		description: "Senha segura para a conta",
		example: "MinhaSenh@123",
		minLength: 6,
		maxLength: 128,
		format: "password",
		pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]",
		examples: {
			strong: {
				summary: "Senha Forte",
				value: "MinhaSenh@123",
				description: "Contém maiúscula, minúscula, número e símbolo",
			},
			medium: {
				summary: "Senha Média",
				value: "Password123",
				description: "Contém maiúscula, minúscula e número",
			},
		},
	})
	@IsNotEmpty({ message: "Senha é obrigatória" })
	@IsString({ message: "Senha deve ser uma string" })
	@MinLength(6, { message: "Senha deve ter pelo menos 6 caracteres" })
	@MaxLength(128, { message: "Senha deve ter no máximo 128 caracteres" })
	password: string;

	@ApiProperty({
		description: "Nome completo do usuário (opcional)",
		example: "João Silva",
		maxLength: 100,
		required: false,
		examples: {
			full: {
				summary: "Nome Completo",
				value: "João Silva Santos",
			},
			simple: {
				summary: "Nome Simples",
				value: "João Silva",
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Nome completo deve ser uma string" })
	@MaxLength(100, { message: "Nome completo deve ter no máximo 100 caracteres" })
	fullName?: string;

	@ApiProperty({
		description: "URL da imagem de avatar do usuário (opcional)",
		example: "https://example.com/avatars/joao.jpg",
		format: "uri",
		required: false,
		examples: {
			gravatar: {
				summary: "Gravatar",
				value: "https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50",
			},
			custom: {
				summary: "Servidor Próprio",
				value: "https://cdn.example.com/avatars/user123.jpg",
			},
		},
	})
	@IsOptional()
	@IsUrl({}, { message: "Avatar deve ser uma URL válida" })
	avatar?: string;
}
