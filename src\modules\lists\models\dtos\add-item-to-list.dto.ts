import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsUUID, IsOptional, IsString, IsInt, Min, MaxLength } from "class-validator";

export class AddItemToListDto {
	@ApiProperty({
		description: "ID da obra a ser adicionada",
		example: "123e4567-e89b-12d3-a456-************",
	})
	@IsUUID()
	workId: string;

	@ApiPropertyOptional({
		description: "Nota pessoal sobre a obra na lista",
		example: "Obra incrível, recomendo para todos!",
	})
	@IsOptional()
	@IsString()
	@MaxLength(500)
	note?: string;

	@ApiPropertyOptional({
		description: "Posição da obra na lista (para ordenação)",
		example: 1,
		minimum: 0,
	})
	@IsOptional()
	@IsInt()
	@Min(0)
	order?: number;
}
