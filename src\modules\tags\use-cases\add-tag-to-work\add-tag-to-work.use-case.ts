import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { ITagRepository, IWorkTag, IWorkTagRepository } from "../../models/interfaces";

@Injectable()
export class AddTagToWorkUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository,

		@Inject("IWorkTagRepository")
		private workTagRepository: IWorkTagRepository,

		@Inject("IWorkRepository")
		private workRepository: any // Usando any pois o IWorkRepository não foi importado
	) {}

	async execute(workId: string, tagId: string): Promise<IWorkTag> {
		// Verificar se o tag existe
		const tag = await this.tagRepository.findById(tagId);

		if (!tag) {
			throw new ResourceNotFoundException("Tag", tagId);
		}

		// Verificar se a obra existe
		const work = await this.workRepository.findById(workId);

		if (!work) {
			throw new ResourceNotFoundException("Obra", workId);
		}

		// Verificar se a tag já está associada à obra
		const existingWorkTag = await this.workTagRepository.findByWorkIdAndTagId(workId, tagId);

		if (existingWorkTag) {
			throw new DuplicateResourceException("Tag associada à obra", "workId:tagId", `${workId}:${tagId}`);
		}

		// Criar a associação entre obra e tag
		const workTag = await this.workTagRepository.create({ workId, tagId });

		// Incrementar o contador de obras na tag
		await this.tagRepository.incrementWorksCount(tagId);

		return workTag;
	}
}
