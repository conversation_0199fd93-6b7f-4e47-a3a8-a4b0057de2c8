import { ApiPropertyOptional } from '@nestjs/swagger';
import {
	IsEnum,
	IsOptional,
	IsInt,
	IsN<PERSON>ber,
	Min,
	Max,
} from 'class-validator';
import { ReadingStatus } from '../enums';

export class UpdateReadingDto {
	@ApiPropertyOptional({
		description: 'Status de leitura',
		enum: ReadingStatus,
		example: ReadingStatus.COMPLETED,
	})
	@IsOptional()
	@IsEnum(ReadingStatus)
	status?: ReadingStatus;

	@ApiPropertyOptional({
		description: 'Capítulo atual',
		example: 25,
		minimum: 0,
	})
	@IsOptional()
	@IsInt()
	@Min(0)
	currentChapter?: number;

	@ApiPropertyOptional({
		description: 'Avaliação pessoal (0-5)',
		example: 4.8,
		minimum: 0,
		maximum: 5,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	@Max(5)
	personalRating?: number;
}
