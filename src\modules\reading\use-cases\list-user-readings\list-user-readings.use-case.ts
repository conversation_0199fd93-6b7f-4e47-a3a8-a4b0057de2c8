import { Injectable, Inject } from "@nestjs/common";
import { IUserWorkRepository, IUserWorkFilters, IUserWork } from "../../models/interfaces";

export interface IListUserReadingsResponse {
	userWorks: IUserWork[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

@Injectable()
export class ListUserReadingsUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(filters: IUserWorkFilters): Promise<IListUserReadingsResponse> {
		const { userWorks, total } = await this.userWorkRepository.findByUser(filters);

		const page = filters.page || 1;
		const limit = filters.limit || 20;
		const totalPages = Math.ceil(total / limit);

		return {
			userWorks,
			total,
			page,
			limit,
			totalPages,
		};
	}
}
