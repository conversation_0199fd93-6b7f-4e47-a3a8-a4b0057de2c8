import { Injectable, Inject } from "@nestjs/common";
import { IListRepository, IListFilters, IList } from "../../models/interfaces";

export interface IListUserListsResponse {
	lists: IList[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

@Injectable()
export class ListUserListsUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(filters: IListFilters): Promise<IListUserListsResponse> {
		const { lists, total } = await this.listRepository.findByUser(filters);

		const page = filters.page || 1;
		const limit = filters.limit || 20;
		const totalPages = Math.ceil(total / limit);

		return {
			lists,
			total,
			page,
			limit,
			totalPages,
		};
	}
}
