import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, HttpStatus } from "@nestjs/common";
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth,
	ApiParam,
	ApiExtraModels,
	ApiBadRequestResponse,
	ApiUnauthorizedResponse,
	ApiNotFoundResponse,
	ApiConflictResponse,
	ApiInternalServerErrorResponse,
} from "@nestjs/swagger";

import { CreateListDto, UpdateListDto, AddItemToListDto, UpdateListItemDto, ListFiltersDto, ListItemFiltersDto } from "../models/dtos";

import {
	CreateListUseCase,
	UpdateListUseCase,
	DeleteListUseCase,
	GetListUseCase,
	ListUserListsUseCase,
	AddItemToListUseCase,
	RemoveItemFromListUseCase,
	GetListItemsUseCase,
} from "../use-cases";

import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import { CurrentUser, ICurrentUserPayload } from "../../../shared/decorators/current-user.decorator";
import {
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";
import { DeleteSuccessResponseDto } from "../../../shared/dtos/success-response.dto";

@ApiTags("lists")
@ApiExtraModels(
	CreateListDto,
	UpdateListDto,
	AddItemToListDto,
	UpdateListItemDto,
	DeleteSuccessResponseDto,
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto
)
@Controller("lists")
export class ListsController {
	constructor(
		private readonly createListUseCase: CreateListUseCase,
		private readonly updateListUseCase: UpdateListUseCase,
		private readonly deleteListUseCase: DeleteListUseCase,
		private readonly getListUseCase: GetListUseCase,
		private readonly listUserListsUseCase: ListUserListsUseCase,
		private readonly addItemToListUseCase: AddItemToListUseCase,
		private readonly removeItemFromListUseCase: RemoveItemFromListUseCase,
		private readonly getListItemsUseCase: GetListItemsUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Criar nova lista",
		description: "Cria uma nova lista personalizada para o usuário",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Lista criada com sucesso",
	})
	@ApiBadRequestResponse({
		description: "Dados inválidos",
		type: ValidationErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiConflictResponse({
		description: "Já existe uma lista com este nome",
		type: ConflictErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async createList(@CurrentUser() currentUser: ICurrentUserPayload, @Body() createListDto: CreateListDto) {
		return await this.createListUseCase.execute(String(currentUser.id), createListDto);
	}

	@Get()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Listar listas do usuário",
		description: "Lista todas as listas do usuário com filtros opcionais",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Listas recuperadas com sucesso",
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async listUserLists(@CurrentUser() currentUser: ICurrentUserPayload, @Query() filters: ListFiltersDto) {
		const filtersWithUserId = {
			...filters,
			userId: String(currentUser.id),
		};
		return await this.listUserListsUseCase.execute(filtersWithUserId);
	}

	@Get(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Obter lista específica",
		description: "Retorna uma lista específica (própria ou pública)",
	})
	@ApiParam({
		name: "id",
		description: "ID da lista",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Lista encontrada",
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "Lista não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async getList(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string) {
		return await this.getListUseCase.execute(String(currentUser.id), id);
	}

	@Put(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar lista",
		description: "Atualiza uma lista existente do usuário",
	})
	@ApiParam({
		name: "id",
		description: "ID da lista",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Lista atualizada com sucesso",
	})
	@ApiBadRequestResponse({
		description: "Dados inválidos",
		type: ValidationErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "Lista não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiConflictResponse({
		description: "Já existe uma lista com este nome",
		type: ConflictErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async updateList(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string, @Body() updateListDto: UpdateListDto) {
		return await this.updateListUseCase.execute(String(currentUser.id), id, updateListDto);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Deletar lista",
		description: "Remove uma lista do usuário",
	})
	@ApiParam({
		name: "id",
		description: "ID da lista",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Lista removida com sucesso",
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "Lista não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async deleteList(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string): Promise<DeleteSuccessResponseDto> {
		await this.deleteListUseCase.execute(String(currentUser.id), id);
		return {
			success: true,
			message: "Lista removida com sucesso",
			deletedId: id,
			deletedAt: new Date().toISOString(),
		};
	}

	@Post(":id/items")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Adicionar obra à lista",
		description: "Adiciona uma obra à lista do usuário",
	})
	@ApiParam({
		name: "id",
		description: "ID da lista",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Obra adicionada à lista com sucesso",
	})
	@ApiBadRequestResponse({
		description: "Dados inválidos",
		type: ValidationErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "Lista ou obra não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiConflictResponse({
		description: "Obra já está na lista",
		type: ConflictErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async addItemToList(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") listId: string, @Body() addItemToListDto: AddItemToListDto) {
		return await this.addItemToListUseCase.execute(String(currentUser.id), listId, addItemToListDto);
	}

	@Get(":id/items")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Listar itens da lista",
		description: "Lista todas as obras de uma lista específica",
	})
	@ApiParam({
		name: "id",
		description: "ID da lista",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Itens da lista recuperados com sucesso",
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "Lista não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async getListItems(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") listId: string, @Query() filters: ListItemFiltersDto) {
		return await this.getListItemsUseCase.execute(String(currentUser.id), listId, filters);
	}

	@Delete(":id/items/:itemId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Remover obra da lista",
		description: "Remove uma obra específica da lista",
	})
	@ApiParam({
		name: "id",
		description: "ID da lista",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiParam({
		name: "itemId",
		description: "ID do item na lista",
		example: "123e4567-e89b-12d3-a456-426614174001",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Obra removida da lista com sucesso",
	})
	@ApiUnauthorizedResponse({
		description: "Token inválido ou expirado",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "Lista ou item não encontrado",
		type: NotFoundErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async removeItemFromList(
		@CurrentUser() currentUser: ICurrentUserPayload,
		@Param("id") listId: string,
		@Param("itemId") itemId: string
	): Promise<DeleteSuccessResponseDto> {
		await this.removeItemFromListUseCase.execute(String(currentUser.id), listId, itemId);
		return {
			success: true,
			message: "Obra removida da lista com sucesso",
			deletedId: itemId,
			deletedAt: new Date().toISOString(),
		};
	}
}
