import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { Role } from "./role.entity";

/**
 * Entidade que representa a relação entre usuários e roles
 * Permite que um usuário tenha múltiplos roles
 *
 * @entity user_roles
 */
@Entity("user_roles")
@Index(["userId", "roleId"], { unique: true })
export class UserRole {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "integer" })
	@Index()
	userId: number;

	@Column({ type: "uuid" })
	@Index()
	roleId: string;

	@Column({ default: true })
	isActive: boolean;

	@Column({ type: "timestamp", nullable: true })
	expiresAt?: Date; // Para roles temporários

	@Column({ type: "integer", nullable: true })
	assignedBy?: number; // ID do usuário que atribuiu o role

	@ManyToOne(() => Role, { onDelete: "CASCADE" })
	@JoinColumn({ name: "roleId" })
	role: Role;

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
