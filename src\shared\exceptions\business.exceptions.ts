import { HttpException, HttpStatus } from "@nestjs/common";

export class BusinessException extends HttpException {
	constructor(
		message: string,
		statusCode: HttpStatus = HttpStatus.BAD_REQUEST,
		public readonly errorCode?: string,
		public readonly details?: any
	) {
		super(
			{
				message,
				error: errorCode || "BUSINESS_ERROR",
				statusCode,
				details,
			},
			statusCode
		);
	}
}

export class ResourceNotFoundException extends BusinessException {
	constructor(resource: string, identifier: string | number) {
		super(`${resource} com ID '${identifier}' não foi encontrado`, HttpStatus.NOT_FOUND, "RESOURCE_NOT_FOUND", { resource, identifier });
	}
}

export class DuplicateResourceException extends BusinessException {
	constructor(resource: string, field: string, value: any) {
		super(`${resource} com ${field} '${value}' já existe`, HttpStatus.CONFLICT, "DUPLICATE_RESOURCE", { resource, field, value });
	}
}

export class ValidationException extends BusinessException {
	constructor(errors: Record<string, string[]>) {
		super("Dados de entrada inválidos", HttpStatus.BAD_REQUEST, "VALIDATION_ERROR", { validationErrors: errors });
	}
}

export class UnauthorizedOperationException extends BusinessException {
	constructor(operation: string, reason?: string) {
		super(`Operação '${operation}' não autorizada${reason ? `: ${reason}` : ""}`, HttpStatus.FORBIDDEN, "UNAUTHORIZED_OPERATION", { operation, reason });
	}
}

export class DatabaseException extends BusinessException {
	constructor(operation: string, originalError?: Error) {
		super(`Erro na operação de banco de dados: ${operation}`, HttpStatus.INTERNAL_SERVER_ERROR, "DATABASE_ERROR", {
			operation,
			originalMessage: originalError?.message,
		});
	}
}

export class AuthenticationException extends BusinessException {
	constructor(reason?: string) {
		const message = reason || "Acesso negado. Você precisa estar autenticado para acessar este recurso.";
		super(message, HttpStatus.UNAUTHORIZED, "AUTHENTICATION_REQUIRED", { reason });
	}
}

export class InvalidCredentialsException extends BusinessException {
	constructor(field?: string) {
		const message = field ? `Credenciais inválidas: ${field} incorreto` : "Credenciais inválidas. Verifique seu email/usuário e senha.";
		super(message, HttpStatus.UNAUTHORIZED, "INVALID_CREDENTIALS", { field });
	}
}

export class TokenExpiredException extends BusinessException {
	constructor(tokenType: string) {
		const message = tokenType === "access" ? "Sua sessão expirou. Faça login novamente." : `Token ${tokenType} expirado`;
		super(message, HttpStatus.UNAUTHORIZED, "TOKEN_EXPIRED", { tokenType });
	}
}

export class InvalidTokenException extends BusinessException {
	constructor(tokenType: string, reason?: string) {
		let message: string;

		if (tokenType === "access") {
			if (reason?.includes("expirado")) {
				message = "Sua sessão expirou. Faça login novamente.";
			} else if (reason?.includes("malformado")) {
				message = "Token de acesso inválido. Faça login novamente.";
			} else {
				message = "Token de acesso inválido ou ausente. Faça login para continuar.";
			}
		} else {
			message = `Token ${tokenType} inválido${reason ? `: ${reason}` : ""}`;
		}

		super(message, HttpStatus.UNAUTHORIZED, "INVALID_TOKEN", { tokenType, reason });
	}
}
