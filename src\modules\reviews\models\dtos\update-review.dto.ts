import { ApiProperty } from "@nestjs/swagger";
import { IsString, <PERSON>Number, IsOptional, IsBoolean, <PERSON>, <PERSON>, <PERSON>eng<PERSON> } from "class-validator";

export class UpdateReviewDto {
	@ApiProperty({
		description: "Nota atribuída à obra (de 0 a 10)",
		example: 8.5,
		minimum: 0,
		maximum: 10,
		required: false,
	})
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Max(10)
	rating?: number;

	@ApiProperty({
		description: "Título da review",
		example: "Uma obra-prima do gênero!",
		required: false,
		maxLength: 100,
	})
	@IsString()
	@IsOptional()
	@MaxLength(100)
	title?: string;

	@ApiProperty({
		description: "Conteúdo da review",
		example: "Essa série tem uma trama envolvente e personagens bem desenvolvidos...",
		required: false,
	})
	@IsString()
	@IsOptional()
	content?: string;

	@ApiProperty({
		description: "Se a review é pública ou privada",
		example: true,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	isPublic?: boolean;

	@ApiProperty({
		description: "Se a review contém spoilers",
		example: false,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	hasContainsSpoilers?: boolean;
}
