import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { ITag, ITagRepository } from "../../models/interfaces";

@Injectable()
export class GetTagUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(id: string): Promise<ITag> {
		const tag = await this.tagRepository.findById(id);

		if (!tag) {
			throw new ResourceNotFoundException("Tag", id);
		}

		return tag;
	}
}
