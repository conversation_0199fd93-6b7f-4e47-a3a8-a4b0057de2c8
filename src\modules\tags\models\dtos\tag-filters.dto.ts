import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsIn } from "class-validator";

export class TagFiltersDto {
	@ApiPropertyOptional({
		description: "Busca por nome da tag",
		example: "Ação",
		required: false,
	})
	@IsOptional()
	@IsString()
	search?: string;

	@ApiPropertyOptional({
		description: "Ordenar por (name, worksCount, createdAt)",
		example: "worksCount",
		enum: ["name", "worksCount", "createdAt"],
		default: "name",
		required: false,
	})
	@IsOptional()
	@IsString()
	@IsIn(["name", "worksCount", "createdAt"])
	sortBy?: string = "name";

	@ApiPropertyOptional({
		description: "Ordem (ASC, DESC)",
		example: "DESC",
		enum: ["ASC", "DESC"],
		default: "ASC",
		required: false,
	})
	@IsOptional()
	@IsString()
	@IsIn(["ASC", "DESC"])
	sortOrder?: string = "ASC";
}
