import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Chapter } from "../models/entities";
import { IChapterRepository, ICreateChapterRequest, IUpdateChapterRequest, IChapter } from "../models/interfaces";

@Injectable()
export class ChapterRepository implements IChapterRepository {
	constructor(
		@InjectRepository(Chapter)
		private readonly chapterRepository: Repository<Chapter>
	) {}

	async create(data: ICreateChapterRequest): Promise<IChapter> {
		const chapter = this.chapterRepository.create(data);
		return await this.chapterRepository.save(chapter);
	}

	async findById(id: string): Promise<IChapter | null> {
		return await this.chapterRepository.findOne({
			where: { id },
			relations: ["work"],
		});
	}

	async findByWorkId(workId: string): Promise<IChapter[]> {
		return await this.chapterRepository.find({
			where: { workId },
			order: { number: "ASC" },
		});
	}

	async findByWorkIdAndNumber(workId: string, number: number): Promise<IChapter | null> {
		return await this.chapterRepository.findOne({
			where: { workId, number },
		});
	}

	async update(id: string, data: IUpdateChapterRequest): Promise<IChapter> {
		await this.chapterRepository.update(id, data);
		const updatedChapter = await this.findById(id);
		if (!updatedChapter) {
			throw new Error("Chapter not found after update");
		}
		return updatedChapter;
	}

	async delete(id: string): Promise<void> {
		await this.chapterRepository.delete(id);
	}

	async getLatestChapterNumber(workId: string): Promise<number> {
		const result = await this.chapterRepository
			.createQueryBuilder("chapter")
			.select("MAX(chapter.number)", "maxNumber")
			.where("chapter.workId = :workId", { workId })
			.getRawOne();

		return result?.maxNumber || 0;
	}
}
