import { Inject, Injectable } from "@nestjs/common";
import { BusinessException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { CreateReviewCommentDto } from "../../models/dtos";
import { IReviewComment, IReviewCommentRepository, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class CreateReviewCommentUseCase {
	constructor(
		@Inject("IReviewCommentRepository")
		private readonly commentRepository: IReviewCommentRepository,

		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(userId: string, createCommentDto: CreateReviewCommentDto): Promise<IReviewComment> {
		// Verificar se a review existe
		const review = await this.reviewRepository.findById(createCommentDto.reviewId);
		if (!review) {
			throw new ResourceNotFoundException("Avaliação", createCommentDto.reviewId);
		}

		// Se for uma resposta, verificar se o comentário pai existe
		if (createCommentDto.parentId) {
			const parentComment = await this.commentRepository.findById(createCommentDto.parentId);

			if (!parentComment) {
				throw new ResourceNotFoundException("Comentário pai", createCommentDto.parentId);
			}

			// Verificar se o comentário pai pertence à mesma review
			if (parentComment.reviewId !== createCommentDto.reviewId) {
				throw new BusinessException("O comentário pai não pertence à review especificada", 400, "INVALID_PARENT_COMMENT");
			}
		}

		// Criar o comentário
		const comment = {
			userId,
			reviewId: createCommentDto.reviewId,
			content: createCommentDto.content,
			parentId: createCommentDto.parentId,
			likes: 0,
			dislikes: 0,
		};

		return await this.commentRepository.create(comment);
	}
}
