import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsOptional, IsUUID, IsInt, IsNumber, Min, Max } from "class-validator";

export class AddItemToRankingDto {
	@ApiProperty({
		description: "ID único da obra a ser adicionada ao ranking",
		example: "123e4567-e89b-12d3-a456-************",
	})
	@IsUUID()
	@IsNotEmpty()
	workId: string;

	@ApiProperty({
		description: "Posição da obra no ranking (opcional, será calculada automaticamente se não informada)",
		example: 1,
		required: false,
	})
	@IsInt()
	@IsOptional()
	@Min(1)
	position?: number;

	@ApiProperty({
		description: "Comentário sobre a obra no ranking (opcional)",
		example: "Excelente enredo e desenvolvimento de personagens!",
		required: false,
	})
	@IsString()
	@IsOptional()
	comment?: string;

	@ApiProperty({
		description: "Nota dada à obra no ranking (1-10)",
		example: 9,
		required: false,
		minimum: 1,
		maximum: 10,
	})
	@IsNumber()
	@IsOptional()
	@Min(1)
	@Max(10)
	rating?: number;
}
