# Script PowerShell para resolver conflitos de migração
# Execute este script quando encontrar o erro "relação já existe"

Write-Host "🔧 Resolvendo conflitos de migração..." -ForegroundColor Yellow

Write-Host "`nOpção 1: Resetar banco de dados (CUIDADO: Apaga todos os dados)" -ForegroundColor Red
Write-Host "AVISO: Isso apagará TODOS OS DADOS do banco!" -ForegroundColor Red
Write-Host "Execute apenas em ambiente de desenvolvimento!" -ForegroundColor Red
Write-Host ""
Write-Host "Para resetar o banco:"
Write-Host "docker-compose down -v"
Write-Host "docker-compose up -d postgres"
Write-Host "npm run migration:run"
Write-Host ""

Write-Host "Opção 2: Executar migrações manualmente (Recomendado)" -ForegroundColor Green
Write-Host "npm run typeorm migration:run"
Write-Host ""

Write-Host "Opção 3: Verificar estado das migrações" -ForegroundColor Cyan
Write-Host "npm run typeorm migration:show"
Write-Host ""

Write-Host "Opção 4: Marcar migração como executada manualmente" -ForegroundColor Yellow
Write-Host "Se as tabelas já existem mas a migração não foi registrada:"
Write-Host "1. Conecte no banco: docker exec -it [container-postgres] psql -U postgres -d backend_test"
Write-Host "2. Execute: INSERT INTO migrations (timestamp, name) VALUES (1718478073827, 'AddRankingsSchema1718478073827');"
Write-Host ""

# Verificar se o Docker está rodando
if (Get-Command docker -ErrorAction SilentlyContinue) {
    Write-Host "✅ Docker encontrado" -ForegroundColor Green
    
    # Verificar containers
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}"
    if ($containers) {
        Write-Host "`nContainers em execução:"
        Write-Host $containers
    }
} else {
    Write-Host "❌ Docker não encontrado. Instale o Docker para usar containers." -ForegroundColor Red
}

Write-Host "`n🔄 Para continuar com a aplicação agora:" -ForegroundColor Cyan
Write-Host "1. Execute uma das opções acima"
Write-Host "2. Reinicie a aplicação: npm run start:dev"
