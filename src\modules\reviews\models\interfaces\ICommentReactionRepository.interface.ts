import { ICommentReaction } from "./ICommentReaction.interface";

export interface ICommentReactionRepository {
	create(reaction: Partial<ICommentReaction>): Promise<ICommentReaction>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<ICommentReaction>;
	findByCommentAndUser(commentId: string, userId: string): Promise<ICommentReaction>;
	findAllByCommentId(commentId: string): Promise<ICommentReaction[]>;
}
