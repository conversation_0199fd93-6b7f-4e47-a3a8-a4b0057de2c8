import { config } from "dotenv";
import { DataSource } from "typeorm";

config();

export const AppDataSource = new DataSource({
	type: "postgres",
	host: process.env.DATABASE_HOST || "localhost",
	port: parseInt(process.env.DATABASE_PORT || "5432"),
	username: process.env.DATABASE_USERNAME || "postgres",
	password: process.env.DATABASE_PASSWORD || "postgres",
	database: process.env.DATABASE_NAME || "backend_test",
	entities: ["src/**/*.entity.{js,ts}"],
	migrations: ["src/migrations/*.{js,ts}"],
	synchronize: false,
	logging: process.env.NODE_ENV === "development",
});
