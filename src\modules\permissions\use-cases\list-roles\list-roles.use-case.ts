import { Injectable, Inject } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IRoleRepository } from "../../models/interfaces";
import { RoleWithPermissionsResponseDto } from "../../models/dtos";
import { Role } from "../../models/entities";

/**
 * Use case para listar todos os roles do sistema
 */
@Injectable()
export class ListRolesUseCase extends BaseUseCase<boolean, RoleWithPermissionsResponseDto[]> {
	constructor(
		@Inject("IRoleRepository")
		private readonly roleRepository: IRoleRepository
	) {
		super("ListRolesUseCase");
	}

	/**
	 * Executa a listagem de roles
	 *
	 * @param includePermissions - Se deve incluir as permissões de cada role
	 * @returns Lista de roles
	 */
	async execute(includePermissions: boolean = true): Promise<RoleWithPermissionsResponseDto[]> {
		this.logger.log("Iniciando listagem de roles");

		try {
			let roles: Role[];

			if (includePermissions) {
				roles = await this.roleRepository.findAllWithPermissions();
			} else {
				roles = await this.roleRepository.findAll();
			}

			this.logger.log(`${roles.length} roles encontrados`);

			return roles.map(role => this.mapToResponseDto(role));
		} catch (error) {
			this.logger.error(`Erro ao listar roles: ${error.message}`, error.stack);
			throw error;
		}
	}

	/**
	 * Mapeia a entidade Role para o DTO de resposta
	 */
	private mapToResponseDto(role: Role): RoleWithPermissionsResponseDto {
		return {
			id: role.id,
			name: role.name,
			description: role.description,
			isActive: role.isActive,
			isSystem: role.isSystem,
			createdAt: role.createdAt,
			updatedAt: role.updatedAt,
			permissions: role.permissions?.map(p => p.name) || [],
		};
	}
}
