import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { User } from "../models/entities/user.entity";
import { IUserRepository } from "../models/interfaces/user-repository.interface";

@Injectable()
export class UserTypeOrmRepository implements IUserRepository {
	constructor(
		@InjectRepository(User)
		private readonly userRepository: Repository<User>
	) {}

	async findByEmail(email: string): Promise<User | null> {
		return this.userRepository.findOne({ where: { email } });
	}

	async findByUsername(username: string): Promise<User | null> {
		return this.userRepository.findOne({ where: { username } });
	}

	async findByUsernameOrEmail(usernameOrEmail: string): Promise<User | null> {
		return this.userRepository
			.createQueryBuilder("user")
			.where("user.username = :usernameOrEmail OR user.email = :usernameOrEmail", { usernameOrEmail })
			.getOne();
	}

	async findById(id: number): Promise<User | null> {
		return this.userRepository.findOne({ where: { id } });
	}

	async create(userData: Partial<User>): Promise<User> {
		const user = this.userRepository.create(userData);
		return this.userRepository.save(user);
	}

	async update(id: number, userData: Partial<User>): Promise<User> {
		await this.userRepository.update(id, userData);
		return this.findById(id);
	}

	async delete(id: number): Promise<void> {
		await this.userRepository.delete(id);
	}

	async findAll(): Promise<User[]> {
		return this.userRepository.find();
	}
}
