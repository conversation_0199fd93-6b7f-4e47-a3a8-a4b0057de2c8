import { Inject, Injectable } from "@nestjs/common";
import { ITag, ITagRepository } from "../../models/interfaces";
import { TagFiltersDto } from "../../models/dtos";

@Injectable()
export class ListTagsUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(filters?: TagFiltersDto): Promise<ITag[]> {
		const mappedFilters = filters
			? {
					search: filters.search,
					sortBy: (["name", "worksCount", "createdAt"].includes(filters.sortBy as string) ? filters.sortBy : undefined) as
						| "name"
						| "worksCount"
						| "createdAt"
						| undefined,
					sortOrder: (["ASC", "DESC"].includes(filters.sortOrder as string) ? filters.sortOrder : undefined) as "ASC" | "DESC" | undefined,
				}
			: undefined;
		return await this.tagRepository.findAll(mappedFilters);
	}
}
