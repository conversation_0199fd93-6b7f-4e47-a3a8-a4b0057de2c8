import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from "typeorm";
import { WorkTag } from "./work-tag.entity";

@Entity("tags")
export class Tag {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "varchar", length: 50 })
	@Index({ unique: true })
	name: string;

	@Column({ type: "varchar", length: 7, nullable: true })
	color: string;

	@Column({ type: "text", nullable: true })
	description?: string;

	@Column({ type: "integer", default: 0 })
	worksCount: number;

	@OneToMany(() => WorkTag, workTag => workTag.tag)
	workTags: WorkTag[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
