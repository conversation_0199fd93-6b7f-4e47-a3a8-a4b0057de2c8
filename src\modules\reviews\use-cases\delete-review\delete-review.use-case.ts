import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IReviewRepository } from "../../models/interfaces";

@Injectable()
export class DeleteReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(id: string, userId: string): Promise<void> {
		// Verificar se a review existe
		const review = await this.reviewRepository.findById(id);

		if (!review) {
			throw new ResourceNotFoundException("Avaliação", id);
		}

		// Verificar se a review pertence ao usuário atual
		if (review.userId !== userId) {
			throw new ResourceNotFoundException("Avaliação do usuário", `${id}:${userId}`);
		}

		// Excluir a review
		await this.reviewRepository.delete(id);
	}
}
