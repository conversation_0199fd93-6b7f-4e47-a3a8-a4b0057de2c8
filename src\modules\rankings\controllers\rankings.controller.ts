import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import {
	ApiBearerAuth,
	ApiBody,
	ApiConflictResponse,
	ApiExtraModels,
	ApiInternalServerErrorResponse,
	ApiNotFoundResponse,
	ApiOperation,
	ApiParam,
	ApiResponse,
	ApiTags,
	ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import { CurrentUser, ICurrentUserPayload } from "../../../shared/decorators/current-user.decorator";

import {
	CreateRankingUseCase,
	UpdateRankingUseCase,
	DeleteRankingUseCase,
	GetRankingUseCase,
	ListUserRankingsUseCase,
	AddItemToRankingUseCase,
	UpdateRankingItemUseCase,
	RemoveItemFromRankingUseCase,
	GetRankingItemsUseCase,
} from "../use-cases";
import { CreateRankingDto, UpdateRankingDto, AddItemToRankingDto, UpdateRankingItemDto, RankingFiltersDto, RankingItemFiltersDto } from "../models/dtos";
import {
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";
import { DeleteSuccessResponseDto } from "src/shared/dtos/success-response.dto";

@ApiTags("rankings")
@ApiExtraModels(
	CreateRankingDto,
	UpdateRankingDto,
	AddItemToRankingDto,
	UpdateRankingItemDto,
	DeleteSuccessResponseDto,
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto
)
@Controller("rankings")
export class RankingsController {
	constructor(
		private readonly createRankingUseCase: CreateRankingUseCase,
		private readonly updateRankingUseCase: UpdateRankingUseCase,
		private readonly deleteRankingUseCase: DeleteRankingUseCase,
		private readonly getRankingUseCase: GetRankingUseCase,
		private readonly listUserRankingsUseCase: ListUserRankingsUseCase,
		private readonly addItemToRankingUseCase: AddItemToRankingUseCase,
		private readonly updateRankingItemUseCase: UpdateRankingItemUseCase,
		private readonly removeItemFromRankingUseCase: RemoveItemFromRankingUseCase,
		private readonly getRankingItemsUseCase: GetRankingItemsUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Criar novo ranking",
		description: "Cria um novo ranking pessoal para o usuário",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Ranking criado com sucesso",
	})
	async createRanking(@CurrentUser() currentUser: ICurrentUserPayload, @Body() createRankingDto: CreateRankingDto) {
		return await this.createRankingUseCase.execute(String(String(currentUser.id)), createRankingDto);
	}

	@Get()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Listar rankings do usuário",
		description: "Lista todos os rankings do usuário autenticado",
	})
	async listUserRankings(@CurrentUser() currentUser: ICurrentUserPayload, @Query() filters: RankingFiltersDto) {
		return await this.listUserRankingsUseCase.execute(String(currentUser.id), filters);
	}

	@Get(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Obter ranking específico",
		description: "Retorna um ranking específico (próprio ou público)",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	async getRanking(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string) {
		return await this.getRankingUseCase.execute(id, String(currentUser.id));
	}

	@Patch(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar ranking",
		description: "Atualiza informações de um ranking existente",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	async updateRanking(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string, @Body() updateRankingDto: UpdateRankingDto) {
		return await this.updateRankingUseCase.execute(id, String(currentUser.id), updateRankingDto);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "Excluir ranking",
		description: "Remove um ranking existente",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	async deleteRanking(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string) {
		await this.deleteRankingUseCase.execute(id, String(currentUser.id));
		return { message: "Ranking excluído com sucesso" };
	}

	@Post(":id/items")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Adicionar item ao ranking",
		description: "Adiciona uma obra ao ranking na posição especificada",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	async addItemToRanking(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") rankingId: string, @Body() addItemDto: AddItemToRankingDto) {
		return await this.addItemToRankingUseCase.execute(rankingId, String(currentUser.id), addItemDto);
	}

	@Get(":id/items")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Obter itens do ranking",
		description: "Lista todos os itens de um ranking específico",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	async getRankingItems(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") rankingId: string, @Query() filters: RankingItemFiltersDto) {
		return await this.getRankingItemsUseCase.execute(rankingId, String(currentUser.id), filters);
	}

	@Patch(":id/items/:itemId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar item do ranking",
		description: "Atualiza informações de um item específico no ranking",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiParam({
		name: "itemId",
		description: "ID do item no ranking",
		example: "123e4567-e89b-12d3-a456-426614174001",
	})
	async updateRankingItem(
		@CurrentUser() currentUser: ICurrentUserPayload,
		@Param("id") rankingId: string,
		@Param("itemId") itemId: string,
		@Body() updateItemDto: UpdateRankingItemDto
	) {
		return await this.updateRankingItemUseCase.execute(rankingId, itemId, String(currentUser.id), updateItemDto);
	}

	@Delete(":id/items/:itemId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "Remover item do ranking",
		description: "Remove uma obra específica do ranking",
	})
	@ApiParam({
		name: "id",
		description: "ID do ranking",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiParam({
		name: "itemId",
		description: "ID do item no ranking",
		example: "123e4567-e89b-12d3-a456-426614174001",
	})
	async removeItemFromRanking(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") rankingId: string, @Param("itemId") itemId: string) {
		await this.removeItemFromRankingUseCase.execute(rankingId, itemId, String(currentUser.id));
		return { message: "Item removido do ranking com sucesso" };
	}
}
