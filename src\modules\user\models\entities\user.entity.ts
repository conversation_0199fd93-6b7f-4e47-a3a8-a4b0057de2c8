import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>um<PERSON>, CreateDateColumn, UpdateDateColumn } from "typeorm";
import { UserRole } from "../enums";

@Entity("users")
export class User {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({ unique: true, length: 50 })
	username: string;

	@Column({ unique: true })
	email: string;

	@Column()
	password: string;

	@Column({ nullable: true, length: 100 })
	fullName?: string;

	@Column({ nullable: true })
	avatar?: string;

	@Column({ default: true })
	isActive: boolean;

	@Column({
		type: "enum",
		enum: UserRole,
		default: UserRole.USER,
	})
	role: UserRole;

	@Column({ nullable: true, type: "text" })
	refreshToken?: string;

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
