import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { List } from "./list.entity";
import { Work } from "../../../works/models/entities";

@Entity("list_items")
@Index(["listId", "workId"], { unique: true }) // Uma obra só pode aparecer uma vez por lista
export class ListItem {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	listId: string;

	@Column({ type: "uuid" })
	workId: string;

	@Column({ type: "text", nullable: true })
	note?: string;

	@Column({ type: "integer", default: 0 })
	order: number; // Para permitir ordenação personalizada

	@ManyToOne(() => List, list => list.items, { onDelete: "CASCADE" })
	@JoinColumn({ name: "listId" })
	list: List;

	@ManyToOne(() => Work, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;

	@CreateDateColumn()
	createdAt: Date;
}
