import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsOptional, IsBoolean, MaxLength } from "class-validator";

export class CreateRankingDto {
	@ApiProperty({
		description: "Nome do ranking",
		example: "Top 10 Manhwas de Ação",
		maxLength: 100,
	})
	@IsString()
	@IsNotEmpty()
	@MaxLength(100)
	name: string;

	@ApiProperty({
		description: "Descrição do ranking (opcional)",
		example: "Minha seleção pessoal dos melhores manhwas de ação",
		required: false,
	})
	@IsString()
	@IsOptional()
	description?: string;

	@ApiProperty({
		description: "Definir se o ranking é público",
		example: true,
		default: false,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	isPublic?: boolean;

	@ApiProperty({
		description: "URL da imagem de capa do ranking",
		example: "https://example.com/images/rankings/action.jpg",
		required: false,
	})
	@IsString()
	@IsOptional()
	@MaxLength(255)
	coverImage?: string;
}
