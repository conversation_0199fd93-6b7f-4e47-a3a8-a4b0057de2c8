import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsNotEmpty, IsOptional, IsString, <PERSON>Url, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, <PERSON> } from "class-validator";

export class CreateUserDto {
	@ApiProperty({
		description: "Nome de usuário único no sistema",
		example: "joao_silva",
		minLength: 3,
		maxLength: 50,
		pattern: "^[a-zA-Z0-9_.-]+$",
		examples: {
			admin: {
				summary: "Usuário Admin",
				value: "admin_user",
			},
			regular: {
				summary: "Usuário Regular",
				value: "joao.silva123",
			},
		},
	})
	@IsNotEmpty({ message: "Username é obrigatório" })
	@IsString({ message: "Username deve ser uma string" })
	@MinLength(3, { message: "Username deve ter pelo menos 3 caracteres" })
	@MaxLength(50, { message: "Username deve ter no máximo 50 caracteres" })
	@Matches(/^[a-zA-Z0-9_.-]+$/, { message: "Username deve conter apenas letras, números, _, . ou -" })
	username: string;

	@ApiProperty({
		description: "Endereço de email válido e único",
		example: "<EMAIL>",
		format: "email",
		examples: {
			personal: {
				summary: "Email Pessoal",
				value: "<EMAIL>",
			},
			business: {
				summary: "Email Corporativo",
				value: "<EMAIL>",
			},
		},
	})
	@IsNotEmpty({ message: "Email é obrigatório" })
	@IsEmail({}, { message: "Email deve ter um formato válido" })
	email: string;

	@ApiProperty({
		description: "Senha para a conta do usuário",
		example: "MinhaSenh@123",
		minLength: 6,
		maxLength: 128,
		format: "password",
		examples: {
			secure: {
				summary: "Senha Segura",
				value: "SuperSecura@2024",
			},
			simple: {
				summary: "Senha Simples",
				value: "senha123",
			},
		},
	})
	@IsNotEmpty({ message: "Senha é obrigatória" })
	@IsString({ message: "Senha deve ser uma string" })
	@MinLength(6, { message: "Senha deve ter pelo menos 6 caracteres" })
	@MaxLength(128, { message: "Senha deve ter no máximo 128 caracteres" })
	password: string;

	@ApiProperty({
		description: "Nome completo do usuário (opcional)",
		example: "João Silva",
		maxLength: 100,
		required: false,
		examples: {
			complete: {
				summary: "Nome Completo",
				value: "João Silva Santos",
			},
			professional: {
				summary: "Nome Profissional",
				value: "Dr. João Silva",
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Nome completo deve ser uma string" })
	@MaxLength(100, { message: "Nome completo deve ter no máximo 100 caracteres" })
	fullName?: string;

	@ApiProperty({
		description: "URL da imagem de avatar (opcional)",
		example: "https://example.com/avatars/joao.jpg",
		format: "uri",
		required: false,
		examples: {
			professional: {
				summary: "Foto Profissional",
				value: "https://company.com/photos/joao.jpg",
			},
			social: {
				summary: "Avatar Social",
				value: "https://gravatar.com/avatar/hash",
			},
		},
	})
	@IsOptional()
	@IsUrl({}, { message: "Avatar deve ser uma URL válida" })
	avatar?: string;
}
