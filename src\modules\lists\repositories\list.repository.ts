import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { List } from "../models/entities";
import { IListRepository, ICreateListRequest, IUpdateListRequest, IListFilters, IList } from "../models/interfaces";

@Injectable()
export class ListRepository implements IListRepository {
	constructor(
		@InjectRepository(List)
		private readonly listRepository: Repository<List>
	) {}

	async create(data: ICreateListRequest): Promise<IList> {
		const list = this.listRepository.create(data);
		return await this.listRepository.save(list);
	}

	async findById(id: string): Promise<IList | null> {
		return await this.listRepository.findOne({
			where: { id },
			relations: ["items", "items.work"],
		});
	}

	async findByUserAndName(userId: string, name: string): Promise<IList | null> {
		return await this.listRepository.findOne({
			where: { userId, name },
		});
	}

	async findByUser(filters: IListFilters): Promise<{ lists: IList[]; total: number }> {
		const queryBuilder = this.listRepository.createQueryBuilder("list").leftJoinAndSelect("list.items", "items").leftJoinAndSelect("items.work", "work");

		this.applyFilters(queryBuilder, filters);
		this.applySorting(queryBuilder, filters);

		const total = await queryBuilder.getCount();

		const { page = 1, limit = 20 } = filters;
		const skip = (page - 1) * limit;

		queryBuilder.skip(skip).take(limit);

		const lists = await queryBuilder.getMany();

		return { lists, total };
	}

	async update(id: string, data: IUpdateListRequest): Promise<IList> {
		await this.listRepository.update(id, data);
		const updatedList = await this.findById(id);
		if (!updatedList) {
			throw new Error("List not found after update");
		}
		return updatedList;
	}

	async delete(id: string): Promise<void> {
		await this.listRepository.delete(id);
	}

	async incrementItemsCount(listId: string): Promise<void> {
		await this.listRepository.increment({ id: listId }, "itemsCount", 1);
	}

	async decrementItemsCount(listId: string): Promise<void> {
		await this.listRepository.decrement({ id: listId }, "itemsCount", 1);
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<List>, filters: IListFilters): void {
		queryBuilder.where("list.userId = :userId", { userId: filters.userId });

		if (filters.isPublic !== undefined) {
			queryBuilder.andWhere("list.isPublic = :isPublic", { isPublic: filters.isPublic });
		}

		if (filters.search) {
			queryBuilder.andWhere("(list.name ILIKE :search OR list.description ILIKE :search)", {
				search: `%${filters.search}%`,
			});
		}
	}

	private applySorting(queryBuilder: SelectQueryBuilder<List>, filters: IListFilters): void {
		const { sortBy = "createdAt", sortOrder = "DESC" } = filters;
		queryBuilder.orderBy(`list.${sortBy}`, sortOrder);
	}
}
