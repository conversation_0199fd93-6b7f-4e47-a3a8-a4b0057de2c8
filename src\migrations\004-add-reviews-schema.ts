import { MigrationInterface, QueryRunner } from "typeorm";

export class AddReviewsSchema1734346153000 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Criar tabela reviews
		await queryRunner.query(`
            CREATE TABLE "reviews" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "userId" uuid NOT NULL,
                "workId" uuid NOT NULL,
                "rating" numeric(3,2) NOT NULL,
                "title" varchar(100),
                "content" text NOT NULL,
                "likes" integer NOT NULL DEFAULT 0,
                "dislikes" integer NOT NULL DEFAULT 0,
                "isPublic" boolean NOT NULL DEFAULT true,
                "hasContainsSpoilers" boolean NOT NULL DEFAULT false,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now()
            )
        `);

		// Criar tabela review_comments
		await queryRunner.query(`
            CREATE TABLE "review_comments" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "reviewId" uuid NOT NULL,
                "userId" uuid NOT NULL,
                "content" text NOT NULL,
                "parentId" uuid, 
                "likes" integer NOT NULL DEFAULT 0,
                "dislikes" integer NOT NULL DEFAULT 0,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "FK_review_comments_reviews" FOREIGN KEY ("reviewId") REFERENCES "reviews" ("id") ON DELETE CASCADE,
                CONSTRAINT "FK_review_comments_parent" FOREIGN KEY ("parentId") REFERENCES "review_comments" ("id") ON DELETE SET NULL
            )
        `);

		// Criar tabela review_reactions
		await queryRunner.query(`
            CREATE TABLE "review_reactions" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "reviewId" uuid NOT NULL,
                "userId" uuid NOT NULL,
                "isLike" boolean NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "FK_review_reactions_reviews" FOREIGN KEY ("reviewId") REFERENCES "reviews" ("id") ON DELETE CASCADE,
                CONSTRAINT "UQ_review_reactions_review_user" UNIQUE ("reviewId", "userId")
            )
        `);

		// Criar tabela comment_reactions
		await queryRunner.query(`
            CREATE TABLE "comment_reactions" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "commentId" uuid NOT NULL,
                "userId" uuid NOT NULL,
                "isLike" boolean NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "FK_comment_reactions_comments" FOREIGN KEY ("commentId") REFERENCES "review_comments" ("id") ON DELETE CASCADE,
                CONSTRAINT "UQ_comment_reactions_comment_user" UNIQUE ("commentId", "userId")
            )
        `);

		// Adicionar índices para performance
		await queryRunner.query(`CREATE INDEX "IDX_reviews_workId" ON "reviews" ("workId")`);
		await queryRunner.query(`CREATE INDEX "IDX_reviews_userId" ON "reviews" ("userId")`);
		await queryRunner.query(`CREATE INDEX "IDX_reviews_rating" ON "reviews" ("rating")`);
		await queryRunner.query(`CREATE UNIQUE INDEX "IDX_reviews_userId_workId" ON "reviews" ("userId", "workId")`);

		await queryRunner.query(`CREATE INDEX "IDX_review_comments_reviewId" ON "review_comments" ("reviewId")`);
		await queryRunner.query(`CREATE INDEX "IDX_review_comments_userId" ON "review_comments" ("userId")`);
		await queryRunner.query(`CREATE INDEX "IDX_review_comments_parentId" ON "review_comments" ("parentId")`);

		await queryRunner.query(`CREATE INDEX "IDX_review_reactions_reviewId" ON "review_reactions" ("reviewId")`);
		await queryRunner.query(`CREATE INDEX "IDX_review_reactions_userId" ON "review_reactions" ("userId")`);

		await queryRunner.query(`CREATE INDEX "IDX_comment_reactions_commentId" ON "comment_reactions" ("commentId")`);
		await queryRunner.query(`CREATE INDEX "IDX_comment_reactions_userId" ON "comment_reactions" ("userId")`);

		// Adicionar Foreign Key para works
		await queryRunner.query(`
            ALTER TABLE "reviews" 
            ADD CONSTRAINT "FK_reviews_works" 
            FOREIGN KEY ("workId") REFERENCES "works"("id") ON DELETE CASCADE
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover índices
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_reviews_workId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_reviews_userId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_reviews_rating"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_reviews_userId_workId"`);

		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_review_comments_reviewId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_review_comments_userId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_review_comments_parentId"`);

		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_review_reactions_reviewId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_review_reactions_userId"`);

		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_comment_reactions_commentId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_comment_reactions_userId"`);

		// Remover tabelas
		await queryRunner.query(`DROP TABLE IF EXISTS "comment_reactions"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "review_reactions"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "review_comments"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "reviews"`);
	}
}
