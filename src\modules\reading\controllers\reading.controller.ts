import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, HttpStatus, Patch } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";

import { AddToReadingDto, UpdateReadingDto, ReadingFiltersDto, UpdateProgressDto } from "../models/dtos";

import {
	AddToReadingUseCase,
	UpdateReadingUseCase,
	DeleteReadingUseCase,
	GetUserReadingUseCase,
	ListUserReadingsUseCase,
	UpdateProgressUseCase,
	GetReadingStatsUseCase,
} from "../use-cases";

import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import { CurrentUser, ICurrentUserPayload } from "../../../shared/decorators/current-user.decorator";
import { DeleteSuccessResponseDto } from "../../../shared/dtos/success-response.dto";

@ApiTags("reading")
@Controller("reading")
export class ReadingController {
	constructor(
		private readonly addToReadingUseCase: AddToReadingUseCase,
		private readonly updateReadingUseCase: UpdateReadingUseCase,
		private readonly deleteReadingUseCase: DeleteReadingUseCase,
		private readonly getUserReadingUseCase: GetUserReadingUseCase,
		private readonly listUserReadingsUseCase: ListUserReadingsUseCase,
		private readonly updateProgressUseCase: UpdateProgressUseCase,
		private readonly getReadingStatsUseCase: GetReadingStatsUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Adicionar obra à lista de leitura",
		description: "Adiciona uma obra à lista de leitura do usuário",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Obra adicionada à lista com sucesso",
	})
	async addToReading(@CurrentUser() currentUser: ICurrentUserPayload, @Body() addToReadingDto: AddToReadingDto) {
		const createData = {
			...addToReadingDto,
			userId: currentUser.id.toString(),
		};
		return await this.addToReadingUseCase.execute(currentUser.id.toString(), createData);
	}

	@Get()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Listar leituras do usuário",
		description: "Lista todas as obras na lista de leitura do usuário com filtros opcionais",
	})
	async listUserReadings(@CurrentUser() currentUser: ICurrentUserPayload, @Query() filters: ReadingFiltersDto) {
		const filtersWithUserId = {
			...filters,
			userId: currentUser.id.toString(),
		};
		return await this.listUserReadingsUseCase.execute(filtersWithUserId);
	}

	@Get("stats")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Obter estatísticas de leitura",
		description: "Retorna estatísticas de leitura do usuário (quantidades por status)",
	})
	async getReadingStats(@CurrentUser() currentUser: ICurrentUserPayload) {
		return await this.getReadingStatsUseCase.execute(currentUser.id.toString());
	}

	@Get(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Obter registro de leitura específico",
		description: "Retorna um registro específico da lista de leitura do usuário",
	})
	async getUserReading(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string) {
		return await this.getUserReadingUseCase.execute(currentUser.id.toString(), id);
	}

	@Put(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar registro de leitura",
		description: "Atualiza um registro específico da lista de leitura",
	})
	async updateReading(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string, @Body() updateReadingDto: UpdateReadingDto) {
		return await this.updateReadingUseCase.execute(currentUser.id.toString(), id, updateReadingDto);
	}

	@Patch("progress/:workId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar progresso de leitura",
		description: "Atualiza o capítulo atual de uma obra específica",
	})
	async updateProgress(@CurrentUser() currentUser: ICurrentUserPayload, @Param("workId") workId: string, @Body() updateProgressDto: UpdateProgressDto) {
		return await this.updateProgressUseCase.execute(currentUser.id.toString(), workId, updateProgressDto.currentChapter);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Remover obra da lista de leitura",
		description: "Remove uma obra da lista de leitura do usuário",
	})
	async deleteReading(@CurrentUser() currentUser: ICurrentUserPayload, @Param("id") id: string): Promise<DeleteSuccessResponseDto> {
		await this.deleteReadingUseCase.execute(currentUser.id.toString(), id);
		return {
			success: true,
			message: "Obra removida da lista de leitura com sucesso",
			deletedId: id,
			deletedAt: new Date().toISOString(),
		};
	}
}
