import { Injectable, Inject } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { IUserRepository } from "../../user/models/interfaces/user-repository.interface";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor(
		private configService: ConfigService,
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			ignoreExpiration: false,
			secretOrKey: configService.get<string>("JWT_SECRET"),
		});
	}

	async validate(payload: any) {
		// Busca o usuário completo para incluir o role
		const user = await this.userRepository.findById(payload.sub);

		if (!user) {
			return null;
		}

		return {
			id: user.id,
			username: user.username,
			email: user.email,
			role: user.role,
			isActive: user.isActive,
		};
	}
}
