import { Injectable, Inject } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IRoleRepository, IPermissionRepository } from "../../models/interfaces";
import { Permission } from "../../models/enums";
import { PermissionEntity, Role } from "../../models/entities";

/**
 * Use case para inicializar o sistema de permissões
 * C<PERSON> todas as permissões e roles básicos se não existirem
 */
@Injectable()
export class SeedPermissionsUseCase extends BaseUseCase {
	constructor(
		@Inject("IRoleRepository")
		private readonly roleRepository: IRoleRepository,
		@Inject("IPermissionRepository")
		private readonly permissionRepository: IPermissionRepository
	) {
		super("SeedPermissionsUseCase");
	}

	/**
	 * Executa a inicialização do sistema de permissões
	 */
	async execute(): Promise<void> {
		this.logger.log("Iniciando seed do sistema de permissões");

		try {
			// Criar todas as permissões
			await this.createPermissions();

			// Criar roles básicos
			await this.createBasicRoles();

			// Atribuir permissões aos roles
			await this.assignPermissionsToRoles();

			this.logger.log("Seed do sistema de permissões concluído com sucesso");
		} catch (error) {
			this.logger.error(`Erro durante o seed de permissões: ${error.message}`, error.stack);
			throw error;
		}
	}

	/**
	 * Cria todas as permissões do sistema
	 */
	private async createPermissions(): Promise<void> {
		this.logger.log("Criando permissões do sistema");

		const permissionsData = [
			// Usuários
			{ name: Permission.USERS_CREATE, description: "Criar novos usuários", resource: "users", action: "create" },
			{ name: Permission.USERS_READ, description: "Visualizar informações de usuários", resource: "users", action: "read" },
			{ name: Permission.USERS_UPDATE, description: "Atualizar dados de usuários", resource: "users", action: "update" },
			{ name: Permission.USERS_DELETE, description: "Deletar usuários", resource: "users", action: "delete" },
			{ name: Permission.USERS_LIST, description: "Listar usuários", resource: "users", action: "list" },
			{ name: Permission.USERS_MANAGE_ROLES, description: "Gerenciar roles de usuários", resource: "users", action: "manage_roles" },

			// Obras
			{ name: Permission.WORKS_CREATE, description: "Criar novas obras", resource: "works", action: "create" },
			{ name: Permission.WORKS_READ, description: "Visualizar obras", resource: "works", action: "read" },
			{ name: Permission.WORKS_UPDATE, description: "Atualizar obras", resource: "works", action: "update" },
			{ name: Permission.WORKS_DELETE, description: "Deletar obras", resource: "works", action: "delete" },
			{ name: Permission.WORKS_LIST, description: "Listar obras", resource: "works", action: "list" },
			{ name: Permission.WORKS_MODERATE, description: "Moderar obras", resource: "works", action: "moderate" },

			// Leitura
			{ name: Permission.READING_CREATE, description: "Adicionar obras à lista de leitura", resource: "reading", action: "create" },
			{ name: Permission.READING_READ, description: "Visualizar progresso de leitura", resource: "reading", action: "read" },
			{ name: Permission.READING_UPDATE, description: "Atualizar progresso de leitura", resource: "reading", action: "update" },
			{ name: Permission.READING_DELETE, description: "Remover obras da lista de leitura", resource: "reading", action: "delete" },
			{ name: Permission.READING_LIST, description: "Listar histórico de leitura", resource: "reading", action: "list" },

			// Listas
			{ name: Permission.LISTS_CREATE, description: "Criar listas personalizadas", resource: "lists", action: "create" },
			{ name: Permission.LISTS_READ, description: "Visualizar listas", resource: "lists", action: "read" },
			{ name: Permission.LISTS_UPDATE, description: "Atualizar listas", resource: "lists", action: "update" },
			{ name: Permission.LISTS_DELETE, description: "Deletar listas", resource: "lists", action: "delete" },
			{ name: Permission.LISTS_LIST, description: "Listar todas as listas", resource: "lists", action: "list" },
			{ name: Permission.LISTS_MODERATE, description: "Moderar listas públicas", resource: "lists", action: "moderate" },

			// Rankings
			{ name: Permission.RANKINGS_CREATE, description: "Criar rankings", resource: "rankings", action: "create" },
			{ name: Permission.RANKINGS_READ, description: "Visualizar rankings", resource: "rankings", action: "read" },
			{ name: Permission.RANKINGS_UPDATE, description: "Atualizar rankings", resource: "rankings", action: "update" },
			{ name: Permission.RANKINGS_DELETE, description: "Deletar rankings", resource: "rankings", action: "delete" },
			{ name: Permission.RANKINGS_LIST, description: "Listar rankings", resource: "rankings", action: "list" },
			{ name: Permission.RANKINGS_MODERATE, description: "Moderar rankings públicos", resource: "rankings", action: "moderate" },

			// Reviews
			{ name: Permission.REVIEWS_CREATE, description: "Criar avaliações", resource: "reviews", action: "create" },
			{ name: Permission.REVIEWS_READ, description: "Visualizar avaliações", resource: "reviews", action: "read" },
			{ name: Permission.REVIEWS_UPDATE, description: "Atualizar avaliações", resource: "reviews", action: "update" },
			{ name: Permission.REVIEWS_DELETE, description: "Deletar avaliações", resource: "reviews", action: "delete" },
			{ name: Permission.REVIEWS_LIST, description: "Listar avaliações", resource: "reviews", action: "list" },
			{ name: Permission.REVIEWS_MODERATE, description: "Moderar avaliações", resource: "reviews", action: "moderate" },

			// Tags
			{ name: Permission.TAGS_CREATE, description: "Criar tags", resource: "tags", action: "create" },
			{ name: Permission.TAGS_READ, description: "Visualizar tags", resource: "tags", action: "read" },
			{ name: Permission.TAGS_UPDATE, description: "Atualizar tags", resource: "tags", action: "update" },
			{ name: Permission.TAGS_DELETE, description: "Deletar tags", resource: "tags", action: "delete" },
			{ name: Permission.TAGS_LIST, description: "Listar tags", resource: "tags", action: "list" },
			{ name: Permission.TAGS_MODERATE, description: "Moderar tags", resource: "tags", action: "moderate" },

			// Administração
			{ name: Permission.ADMIN_DASHBOARD, description: "Acessar dashboard administrativo", resource: "admin", action: "dashboard" },
			{ name: Permission.ADMIN_SYSTEM_CONFIG, description: "Configurar sistema", resource: "admin", action: "system_config" },
			{ name: Permission.ADMIN_LOGS, description: "Visualizar logs do sistema", resource: "admin", action: "logs" },
			{ name: Permission.ADMIN_METRICS, description: "Visualizar métricas do sistema", resource: "admin", action: "metrics" },
			{ name: Permission.ADMIN_BACKUP, description: "Gerenciar backups", resource: "admin", action: "backup" },

			// Moderação
			{ name: Permission.MODERATE_CONTENT, description: "Moderar conteúdo geral", resource: "moderate", action: "content" },
			{ name: Permission.MODERATE_USERS, description: "Moderar usuários", resource: "moderate", action: "users" },
			{ name: Permission.MODERATE_REPORTS, description: "Gerenciar denúncias", resource: "moderate", action: "reports" },

			// Permissões
			{ name: Permission.PERMISSIONS_CREATE, description: "Criar permissões", resource: "permissions", action: "create" },
			{ name: Permission.PERMISSIONS_READ, description: "Visualizar permissões", resource: "permissions", action: "read" },
			{ name: Permission.PERMISSIONS_UPDATE, description: "Atualizar permissões", resource: "permissions", action: "update" },
			{ name: Permission.PERMISSIONS_DELETE, description: "Deletar permissões", resource: "permissions", action: "delete" },
			{ name: Permission.PERMISSIONS_ASSIGN, description: "Atribuir permissões", resource: "permissions", action: "assign" },
		];

		for (const permissionData of permissionsData) {
			const exists = await this.permissionRepository.exists(permissionData.name);
			if (!exists) {
				await this.permissionRepository.create(permissionData);
				this.logger.log(`Permissão criada: ${permissionData.name}`);
			}
		}
	}

	/**
	 * Cria os roles básicos do sistema
	 */
	private async createBasicRoles(): Promise<void> {
		this.logger.log("Criando roles básicos do sistema");

		const rolesData = [
			{ name: "user", description: "Usuário comum do sistema", isSystem: true },
			{ name: "moderator", description: "Moderador de conteúdo", isSystem: true },
			{ name: "admin", description: "Administrador do sistema", isSystem: true },
			{ name: "super_admin", description: "Super administrador", isSystem: true },
		];

		for (const roleData of rolesData) {
			const exists = await this.roleRepository.findByName(roleData.name);
			if (!exists) {
				await this.roleRepository.create(roleData);
				this.logger.log(`Role criado: ${roleData.name}`);
			}
		}
	}

	/**
	 * Atribui permissões aos roles básicos
	 */
	private async assignPermissionsToRoles(): Promise<void> {
		this.logger.log("Atribuindo permissões aos roles");

		// Definir permissões por role
		const rolePermissions = {
			user: [
				Permission.WORKS_READ,
				Permission.WORKS_LIST,
				Permission.READING_CREATE,
				Permission.READING_READ,
				Permission.READING_UPDATE,
				Permission.READING_DELETE,
				Permission.READING_LIST,
				Permission.LISTS_CREATE,
				Permission.LISTS_READ,
				Permission.LISTS_UPDATE,
				Permission.LISTS_DELETE,
				Permission.RANKINGS_CREATE,
				Permission.RANKINGS_READ,
				Permission.RANKINGS_UPDATE,
				Permission.RANKINGS_DELETE,
				Permission.REVIEWS_CREATE,
				Permission.REVIEWS_READ,
				Permission.REVIEWS_UPDATE,
				Permission.REVIEWS_DELETE,
				Permission.TAGS_READ,
				Permission.TAGS_LIST,
			],
			moderator: [
				Permission.WORKS_READ,
				Permission.WORKS_LIST,
				Permission.WORKS_MODERATE,
				Permission.READING_CREATE,
				Permission.READING_READ,
				Permission.READING_UPDATE,
				Permission.READING_DELETE,
				Permission.READING_LIST,
				Permission.LISTS_CREATE,
				Permission.LISTS_READ,
				Permission.LISTS_UPDATE,
				Permission.LISTS_DELETE,
				Permission.LISTS_MODERATE,
				Permission.RANKINGS_CREATE,
				Permission.RANKINGS_READ,
				Permission.RANKINGS_UPDATE,
				Permission.RANKINGS_DELETE,
				Permission.RANKINGS_MODERATE,
				Permission.REVIEWS_CREATE,
				Permission.REVIEWS_READ,
				Permission.REVIEWS_UPDATE,
				Permission.REVIEWS_DELETE,
				Permission.REVIEWS_MODERATE,
				Permission.TAGS_CREATE,
				Permission.TAGS_READ,
				Permission.TAGS_UPDATE,
				Permission.TAGS_DELETE,
				Permission.TAGS_LIST,
				Permission.TAGS_MODERATE,
				Permission.MODERATE_CONTENT,
				Permission.MODERATE_REPORTS,
				Permission.USERS_READ,
				Permission.USERS_LIST,
			],
			admin: [
				Permission.WORKS_CREATE,
				Permission.WORKS_READ,
				Permission.WORKS_UPDATE,
				Permission.WORKS_DELETE,
				Permission.WORKS_LIST,
				Permission.WORKS_MODERATE,
				Permission.READING_CREATE,
				Permission.READING_READ,
				Permission.READING_UPDATE,
				Permission.READING_DELETE,
				Permission.READING_LIST,
				Permission.LISTS_CREATE,
				Permission.LISTS_READ,
				Permission.LISTS_UPDATE,
				Permission.LISTS_DELETE,
				Permission.LISTS_LIST,
				Permission.LISTS_MODERATE,
				Permission.RANKINGS_CREATE,
				Permission.RANKINGS_READ,
				Permission.RANKINGS_UPDATE,
				Permission.RANKINGS_DELETE,
				Permission.RANKINGS_LIST,
				Permission.RANKINGS_MODERATE,
				Permission.REVIEWS_CREATE,
				Permission.REVIEWS_READ,
				Permission.REVIEWS_UPDATE,
				Permission.REVIEWS_DELETE,
				Permission.REVIEWS_LIST,
				Permission.REVIEWS_MODERATE,
				Permission.TAGS_CREATE,
				Permission.TAGS_READ,
				Permission.TAGS_UPDATE,
				Permission.TAGS_DELETE,
				Permission.TAGS_LIST,
				Permission.TAGS_MODERATE,
				Permission.USERS_CREATE,
				Permission.USERS_READ,
				Permission.USERS_UPDATE,
				Permission.USERS_DELETE,
				Permission.USERS_LIST,
				Permission.USERS_MANAGE_ROLES,
				Permission.ADMIN_DASHBOARD,
				Permission.ADMIN_LOGS,
				Permission.ADMIN_METRICS,
				Permission.MODERATE_CONTENT,
				Permission.MODERATE_USERS,
				Permission.MODERATE_REPORTS,
				Permission.PERMISSIONS_READ,
				Permission.PERMISSIONS_ASSIGN,
			],
		};

		for (const [roleName, permissions] of Object.entries(rolePermissions)) {
			const role = await this.roleRepository.findByName(roleName);
			if (role) {
				const permissionEntities = await this.permissionRepository.findByNames(permissions);
				const permissionIds = permissionEntities.map(p => p.id);

				if (permissionIds.length > 0) {
					await this.roleRepository.addPermissions(role.id, permissionIds);
					this.logger.log(`${permissions.length} permissões atribuídas ao role ${roleName}`);
				}
			}
		}

		// Super admin recebe todas as permissões
		const superAdminRole = await this.roleRepository.findByName("super_admin");
		if (superAdminRole) {
			const allPermissions = await this.permissionRepository.findAll();
			const allPermissionIds = allPermissions.map(p => p.id);

			if (allPermissionIds.length > 0) {
				await this.roleRepository.addPermissions(superAdminRole.id, allPermissionIds);
				this.logger.log(`Todas as ${allPermissions.length} permissões atribuídas ao super_admin`);
			}
		}
	}
}
