import { Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IChapter, ICreateChapterRequest } from "../../models/interfaces";
import { ChapterRepository, WorkRepository } from "../../repositories";

@Injectable()
export class CreateChapterUseCase {
	constructor(
		private readonly workRepository: WorkRepository,
		private readonly chapterRepository: ChapterRepository
	) {}

	async execute(data: ICreateChapterRequest): Promise<IChapter> {
		const work = await this.workRepository.findById(data.workId);
		if (!work) throw new ResourceNotFoundException("Obra", data.workId);
		const existingChapter = await this.chapterRepository.findByWorkIdAndNumber(data.workId, data.number);
		if (existingChapter) throw new DuplicateResourceException("<PERSON>ítulo", "number", data.number);
		return await this.chapterRepository.create(data);
	}
}
