import { ValidationPipe } from "@nestjs/common";
import { HttpAdapterHost, NestFactory } from "@nestjs/core";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import helmet from "helmet";
import { AppModule } from "./app.module";
import { AllExceptionsFilter } from "./shared/filters/all-exceptions.filter";
import { ResponseInterceptor } from "./shared/interceptors/response.interceptor";

async function bootstrap() {
	const app = await NestFactory.create(AppModule);

	// Segurança - Helmet para cabeçalhos HTTP seguros
	app.use(helmet());

	// Validação global de DTOs
	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			forbidNonWhitelisted: true,
			transform: true,
		})
	);

	// Interceptor global para padronizar respostas de sucesso
	app.useGlobalInterceptors(new ResponseInterceptor());

	// Filtro global de exceções para padronizar respostas de erro
	const httpAdapterHost = app.get(HttpAdapterHost);
	app.useGlobalFilters(new AllExceptionsFilter(httpAdapterHost));

	// CORS para aceitar requisições de diferentes origens
	app.enableCors();

	const config = new DocumentBuilder()
		.setTitle("� Manhwa/Manga Tracker API")
		.setDescription(
			`
## 📋 Descrição

API completa para rastreamento de manhwas, manhuas e mangás desenvolvida com NestJS, TypeScript e PostgreSQL.

### 🔗 Recursos Principais

- **🔐 Autenticação JWT**: Sistema seguro de login e registro
- **👥 Gerenciamento de Usuários**: CRUD completo de usuários
- **📚 Gerenciamento de Obras**: Cadastro e busca de manhwas/manhuas/mangás
- **📖 Controle de Leitura**: Acompanhamento de progresso de leitura
- **� Listas Personalizadas**: Criação de listas customizadas
- **🏆 Rankings Pessoais**: Sistema de rankings compartilháveis
- **🏷️ Sistema de Tags**: Categorização e filtros avançados
- **⭐ Avaliações**: Sistema de reviews e comentários
- **�🛡️ Validação de Dados**: Validação robusta com class-validator
- **📊 Banco de Dados**: PostgreSQL com TypeORM
- **🏗️ Arquitetura Limpa**: Seguindo princípios SOLID e Clean Architecture

### 🔒 Autenticação

Esta API utiliza **JWT (JSON Web Tokens)** para autenticação. Para acessar endpoints protegidos:

1. Faça login através do endpoint \`/auth/login\`
2. Use o token retornado no header \`Authorization: Bearer {token}\`
3. O token é válido por 7 dias

### 📚 Como Usar

1. **Registrar**: Crie uma conta através de \`POST /auth/register\`
2. **Login**: Faça login através de \`POST /auth/login\`
3. **Gerenciar Obras**: Use os endpoints de \`/works\` para buscar e gerenciar obras
4. **Controlar Leitura**: Use \`/reading\` para acompanhar seu progresso
5. **Criar Listas**: Use \`/lists\` para organizar suas obras
6. **Fazer Rankings**: Use \`/rankings\` para criar rankings pessoais

### 🏷️ Códigos de Status

- \`200\` - Sucesso
- \`201\` - Criado com sucesso
- \`400\` - Dados inválidos
- \`401\` - Não autorizado
- \`403\` - Acesso negado
- \`404\` - Recurso não encontrado
- \`409\` - Conflito (ex: email já existe)
- \`500\` - Erro interno do servidor

### 🌐 Ambiente

- **Desenvolvimento**: \`http://localhost:3000\`
- **Documentação**: \`http://localhost:3000/api\`
		`
		)
		.setVersion("1.0.0")
		.addServer("http://localhost:3000", "Servidor de Desenvolvimento")
		.addBearerAuth(
			{
				type: "http",
				scheme: "bearer",
				bearerFormat: "JWT",
				name: "JWT",
				description: "Insira o token JWT no formato: Bearer {token}",
				in: "header",
			},
			"JWT-auth"
		)
		.addTag("auth", "🔐 Autenticação - Endpoints para login e registro de usuários")
		.addTag("users", "👥 Usuários - CRUD completo de gerenciamento de usuários")
		.addTag("permissions", "🔒 Permissões - Sistema RBAC de roles e permissões")
		.addTag("works", "📚 Obras - Gerenciamento de manhwas, manhuas e mangás")
		.addTag("reading", "📖 Leitura - Controle de progresso de leitura")
		.addTag("lists", "📝 Listas - Listas personalizadas de obras")
		.addTag("rankings", "🏆 Rankings - Rankings pessoais compartilháveis")
		.addTag("tags", "🏷️ Tags - Sistema de categorização e filtros")
		.addTag("reviews", "⭐ Avaliações - Sistema de reviews e comentários")
		.build();

	const document = SwaggerModule.createDocument(app, config, {
		operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
		deepScanRoutes: true,
	});

	SwaggerModule.setup("api", app, document, {
		customSiteTitle: "Backend Test API - Documentação",
		customfavIcon: "/favicon.ico",
		customCss: `
			.swagger-ui .topbar { display: none }
			.swagger-ui .info .title { color: #2c3e50; font-size: 2.5em; }
			.swagger-ui .scheme-container { background: #f8f9fa; padding: 15px; border-radius: 5px; }
			.swagger-ui .info .description { font-size: 1.1em; line-height: 1.6; }
			.swagger-ui .opblock.opblock-post { border-color: #27ae60; }
			.swagger-ui .opblock.opblock-post .opblock-summary { border-color: #27ae60; }
			.swagger-ui .opblock.opblock-get { border-color: #3498db; }
			.swagger-ui .opblock.opblock-get .opblock-summary { border-color: #3498db; }
			.swagger-ui .opblock.opblock-patch { border-color: #f39c12; }
			.swagger-ui .opblock.opblock-patch .opblock-summary { border-color: #f39c12; }
			.swagger-ui .opblock.opblock-delete { border-color: #e74c3c; }
			.swagger-ui .opblock.opblock-delete .opblock-summary { border-color: #e74c3c; }
		`,
		swaggerOptions: {
			persistAuthorization: true,
			displayRequestDuration: true,
			docExpansion: "none",
			filter: true,
			showExtensions: true,
			showCommonExtensions: true,
			defaultModelsExpandDepth: 2,
			defaultModelExpandDepth: 2,
		},
	});

	const port = process.env.PORT || 3000;
	await app.listen(port);
	console.log(`🚀 Aplicação rodando na porta ${port}`);
	console.log(`📚 Documentação disponível em http://localhost:${port}/api`);
}
bootstrap();
