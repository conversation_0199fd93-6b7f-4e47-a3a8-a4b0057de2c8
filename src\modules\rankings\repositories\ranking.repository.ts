import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { Ranking } from "../models/entities/ranking.entity";
import { IRanking, IRankingFilters, IRankingRepository } from "../models/interfaces";

@Injectable()
export class RankingRepository implements IRankingRepository {
	constructor(
		@InjectRepository(Ranking)
		private readonly rankingRepository: Repository<Ranking>
	) {}

	async create(ranking: Partial<IRanking>): Promise<IRanking> {
		const newRanking = this.rankingRepository.create(ranking);
		return await this.rankingRepository.save(newRanking);
	}

	async update(id: string, ranking: Partial<IRanking>): Promise<IRanking> {
		await this.rankingRepository.update(id, ranking);
		return await this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.rankingRepository.delete(id);
	}

	async findById(id: string): Promise<IRanking> {
		return await this.rankingRepository.findOne({
			where: { id },
		});
	}

	async findByIdWithItems(id: string): Promise<IRanking> {
		return await this.rankingRepository.findOne({
			where: { id },
			relations: ["items"],
		});
	}

	async findByUserId(userId: string, filters?: IRankingFilters): Promise<IRanking[]> {
		const queryBuilder = this.rankingRepository.createQueryBuilder("ranking");

		queryBuilder.where("ranking.userId = :userId", { userId });

		if (filters) {
			this.applyFilters(queryBuilder, filters);
		}

		queryBuilder.orderBy("ranking.createdAt", "DESC");

		return await queryBuilder.getMany();
	}

	async findPublicRankings(filters?: IRankingFilters): Promise<IRanking[]> {
		const queryBuilder = this.rankingRepository.createQueryBuilder("ranking");

		queryBuilder.where("ranking.isPublic = :isPublic", { isPublic: true });

		if (filters) {
			this.applyFilters(queryBuilder, filters);
		}

		queryBuilder.orderBy("ranking.createdAt", "DESC");

		return await queryBuilder.getMany();
	}

	async incrementItemsCount(id: string): Promise<void> {
		await this.rankingRepository.increment({ id }, "itemsCount", 1);
	}

	async decrementItemsCount(id: string): Promise<void> {
		await this.rankingRepository.decrement({ id }, "itemsCount", 1);
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<Ranking>, filters: IRankingFilters): void {
		if (filters.isPublic !== undefined) {
			queryBuilder.andWhere("ranking.isPublic = :isPublic", {
				isPublic: filters.isPublic,
			});
		}

		if (filters.search) {
			queryBuilder.andWhere("(ranking.name ILIKE :search OR ranking.description ILIKE :search)", {
				search: `%${filters.search}%`,
			});
		}
	}
}
