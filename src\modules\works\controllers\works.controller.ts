import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, HttpStatus } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiForbiddenResponse } from "@nestjs/swagger";

import { CreateWorkDto, UpdateWorkDto, WorkFiltersDto, CreateChapterDto } from "../models/dtos";
import { CreateWorkUseCase, GetWorkUseCase, ListWorksUseCase, UpdateWorkUseCase, DeleteWorkUseCase, CreateChapterUseCase } from "../use-cases";
import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import { RolesGuard } from "../../permissions/models/guards";
import { Roles } from "../../permissions/models/decorators";
import { UserRole } from "../../user/models/enums";

@ApiTags("works")
@Controller("works")
export class WorksController {
	constructor(
		private readonly createWorkUseCase: CreateWorkUseCase,
		private readonly getWorkUseCase: GetWorkUseCase,
		private readonly listWorksUseCase: ListWorksUseCase,
		private readonly updateWorkUseCase: UpdateWorkUseCase,
		private readonly deleteWorkUseCase: DeleteWorkUseCase,
		private readonly createChapterUseCase: CreateChapterUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Criar nova obra",
		description: "Cria uma nova obra (manhwa, manhua ou manga)",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Obra criada com sucesso",
	})
	@ApiResponse({
		status: HttpStatus.CONFLICT,
		description: "Uma obra com este título já existe",
	})
	async createWork(@Body() createWorkDto: CreateWorkDto) {
		return await this.createWorkUseCase.execute(createWorkDto);
	}

	@Get()
	@ApiOperation({
		summary: "Listar obras",
		description: "Lista obras com filtros e paginação",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Lista de obras retornada com sucesso",
	})
	async listWorks(@Query() filters: WorkFiltersDto) {
		return await this.listWorksUseCase.execute(filters);
	}

	@Get(":id")
	@ApiOperation({
		summary: "Buscar obra por ID",
		description: "Retorna uma obra específica pelo ID",
	})
	@ApiParam({
		name: "id",
		description: "ID da obra",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Obra encontrada com sucesso",
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: "Obra não encontrada",
	})
	async getWork(@Param("id") id: string) {
		return await this.getWorkUseCase.execute(id);
	}

	@Put(":id")
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar obra",
		description: "Atualiza uma obra existente",
	})
	@ApiParam({
		name: "id",
		description: "ID da obra",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.OK,
		description: "Obra atualizada com sucesso",
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: "Obra não encontrada",
	})
	@ApiResponse({
		status: HttpStatus.CONFLICT,
		description: "Uma obra com este título já existe",
	})
	async updateWork(@Param("id") id: string, @Body() updateWorkDto: UpdateWorkDto) {
		return await this.updateWorkUseCase.execute(id, updateWorkDto);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Deletar obra",
		description: "Remove uma obra do sistema",
	})
	@ApiParam({
		name: "id",
		description: "ID da obra",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiResponse({
		status: HttpStatus.NO_CONTENT,
		description: "Obra deletada com sucesso",
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: "Obra não encontrada",
	})
	async deleteWork(@Param("id") id: string) {
		await this.deleteWorkUseCase.execute(id);
	}

	@Post("chapters")
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Criar novo capítulo",
		description: "Adiciona um novo capítulo a uma obra",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Capítulo criado com sucesso",
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: "Obra não encontrada",
	})
	@ApiResponse({
		status: HttpStatus.CONFLICT,
		description: "Capítulo já existe para esta obra",
	})
	async createChapter(@Body() createChapterDto: CreateChapterDto) {
		return await this.createChapterUseCase.execute(createChapterDto);
	}
}
