import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, MaxLength, Matches } from "class-validator";

export class UpdateTagDto {
	@ApiProperty({
		description: "Nome da tag",
		example: "Ação e Aventura",
		maxLength: 50,
		required: false,
	})
	@IsString()
	@IsOptional()
	@MaxLength(50)
	name?: string;

	@ApiProperty({
		description: "Cor da tag em formato hexadecimal",
		example: "#3366FF",
		required: false,
	})
	@IsString()
	@IsOptional()
	@MaxLength(7)
	@Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
		message: "A cor deve estar no formato hexadecimal (ex: #FF5733)",
	})
	color?: string;

	@ApiProperty({
		description: "Descrição da tag",
		example: "Obras com cenas de ação e elementos de aventura",
		required: false,
	})
	@IsString()
	@IsOptional()
	description?: string;
}
