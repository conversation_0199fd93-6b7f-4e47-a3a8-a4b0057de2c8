import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IRanking, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class GetRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(id: string, userId?: string): Promise<IRanking> {
		const ranking = await this.rankingRepository.findByIdWithItems(id);
		if (!ranking) {
			throw new ResourceNotFoundException("Ranking", id);
		}
		// Se o ranking não for público e não pertencer ao usuário, não permita acesso
		if (!ranking.isPublic && ranking.userId !== userId) {
			throw new ResourceNotFoundException("Ranking privado", `${id}:${userId}`);
		}

		return ranking;
	}
}
