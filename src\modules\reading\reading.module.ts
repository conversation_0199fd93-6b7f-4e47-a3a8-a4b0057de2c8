import { Modu<PERSON>, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserWork } from "./models/entities";
import { UserWorkRepository } from "./repositories";
import {
	AddToReadingUseCase,
	UpdateReadingUseCase,
	DeleteReadingUseCase,
	GetUserReadingUseCase,
	ListUserReadingsUseCase,
	UpdateProgressUseCase,
	GetReadingStatsUseCase,
} from "./use-cases";
import { ReadingController } from "./controllers";
import { WorksModule } from "../works/works.module";

@Module({
	imports: [
		TypeOrmModule.forFeature([UserWork]),
		forwardRef(() => WorksModule), // Para evitar dependência circular
	],
	controllers: [ReadingController],
	providers: [
		UserWorkRepository,
		AddToReadingUseCase,
		UpdateReadingUseCase,
		DeleteReadingUseCase,
		GetUserReadingUseCase,
		ListUserReadingsUseCase,
		UpdateProgressUseCase,
		GetReadingStatsUseCase,
		{
			provide: "IUserWorkRepository",
			useExisting: UserWorkRepository,
		},
	],
	exports: [
		"IUserWorkRepository",
		UserWorkRepository,
		AddToReadingUseCase,
		UpdateReadingUseCase,
		DeleteReadingUseCase,
		GetUserReadingUseCase,
		ListUserReadingsUseCase,
		UpdateProgressUseCase,
		GetReadingStatsUseCase,
	],
})
export class ReadingModule {}
