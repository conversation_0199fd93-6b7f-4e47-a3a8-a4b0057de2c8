import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, IsUUID } from "class-validator";

/**
 * DTO para atualização de um role existente
 */
export class UpdateRoleDto {
	@ApiProperty({
		description: "Nome do role",
		example: "content_moderator_updated",
		minLength: 3,
		maxLength: 50,
		required: false,
		examples: {
			rename: {
				summary: "Renomear Role",
				value: "senior_moderator",
			},
			update: {
				summary: "Atualizar Nome",
				value: "content_moderator_v2",
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Nome deve ser uma string" })
	@MinLength(3, { message: "Nome deve ter pelo menos 3 caracteres" })
	@MaxLength(50, { message: "Nome deve ter no máximo 50 caracteres" })
	name?: string;

	@ApiProperty({
		description: "Descrição do role",
		example: "Moderador sênior com permissões avançadas",
		maxLength: 255,
		required: false,
		examples: {
			update: {
				summary: "Atualizar Descrição",
				value: "Moderador sênior responsável por supervisionar outros moderadores",
			},
			clear: {
				summary: "Limpar Descrição",
				value: null,
			},
		},
	})
	@IsOptional()
	@IsString({ message: "Descrição deve ser uma string" })
	@MaxLength(255, { message: "Descrição deve ter no máximo 255 caracteres" })
	description?: string;

	@ApiProperty({
		description: "Se o role está ativo",
		example: false,
		required: false,
		examples: {
			activate: {
				summary: "Ativar Role",
				value: true,
			},
			deactivate: {
				summary: "Desativar Role",
				value: false,
			},
		},
	})
	@IsOptional()
	@IsBoolean({ message: "isActive deve ser um valor booleano" })
	isActive?: boolean;

	@ApiProperty({
		description: "IDs das permissões a serem atribuídas ao role (substitui as existentes)",
		example: ["uuid-1", "uuid-2", "uuid-3"],
		type: [String],
		required: false,
		examples: {
			add: {
				summary: "Adicionar Permissões",
				value: ["perm-admin-dashboard", "perm-manage-users"],
			},
			remove: {
				summary: "Remover Todas",
				value: [],
			},
		},
	})
	@IsOptional()
	@IsArray({ message: "Permissões devem ser um array" })
	@IsUUID(4, { each: true, message: "Cada permissão deve ser um UUID válido" })
	permissionIds?: string[];
}
