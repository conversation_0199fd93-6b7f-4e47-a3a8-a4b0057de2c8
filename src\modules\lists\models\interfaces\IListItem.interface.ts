export interface IListItem {
	id: string;
	listId: string;
	workId: string;
	note?: string;
	order: number;
	createdAt: Date;
}

export interface ICreateListItemRequest {
	listId: string;
	workId: string;
	note?: string;
	order?: number;
}

export interface IUpdateListItemRequest {
	note?: string;
	order?: number;
}

export interface IListItemFilters {
	listId: string;
	page?: number;
	limit?: number;
	sortBy?: "order" | "createdAt";
	sortOrder?: "ASC" | "DESC";
}

export interface IListItemRepository {
	create(data: ICreateListItemRequest): Promise<IListItem>;
	findById(id: string): Promise<IListItem | null>;
	findByListAndWork(listId: string, workId: string): Promise<IListItem | null>;
	findByList(filters: IListItemFilters): Promise<{ items: IListItem[]; total: number }>;
	update(id: string, data: IUpdateListItemRequest): Promise<IListItem>;
	delete(id: string): Promise<void>;
	getNextOrder(listId: string): Promise<number>;
	reorderItems(listId: string, itemOrders: { id: string; order: number }[]): Promise<void>;
}
