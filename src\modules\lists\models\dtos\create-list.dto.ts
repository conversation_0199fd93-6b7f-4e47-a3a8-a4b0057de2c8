import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsOptional, IsBoolean, IsUrl, MaxLength, MinLength } from "class-validator";

export class CreateListDto {
	@ApiProperty({
		description: "Nome da lista",
		example: "Meus Favoritos",
		minLength: 1,
		maxLength: 100,
	})
	@IsString()
	@MinLength(1)
	@MaxLength(100)
	name: string;

	@ApiPropertyOptional({
		description: "Descrição da lista",
		example: "Minhas obras favoritas de todos os tempos",
	})
	@IsOptional()
	@IsString()
	@MaxLength(500)
	description?: string;

	@ApiPropertyOptional({
		description: "Se a lista é pública",
		example: false,
		default: false,
	})
	@IsOptional()
	@IsBoolean()
	isPublic?: boolean;

	@ApiPropertyOptional({
		description: "URL da imagem de capa da lista",
		example: "https://example.com/cover.jpg",
	})
	@IsOptional()
	@IsUrl()
	coverImage?: string;
}
