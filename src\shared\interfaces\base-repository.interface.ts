export interface IBaseRepository<T, ID = string> {
	// Operações básicas CRUD
	create(data: Partial<T>): Promise<T>;
	findById(id: ID): Promise<T | null>;
	update(id: ID, data: Partial<T>): Promise<T>;
	delete(id: ID): Promise<void>;
	findAll(options?: FindAllOptions<T>): Promise<PaginatedResult<T>>;
}

export interface FindAllOptions<T> {
	page?: number;
	limit?: number;
	sort?: SortOption<T>;
	filters?: Partial<T>;
}

export interface SortOption<T> {
	field: keyof T;
	direction: "ASC" | "DESC";
}

export interface PaginatedResult<T> {
	data: T[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

// Interface específica para repositories que precisam de busca por campo único
export interface IUniqueFieldRepository<T, ID = string> extends IBaseRepository<T, ID> {
	findByUniqueField(field: keyof T, value: any): Promise<T | null>;
	existsByUniqueField(field: keyof T, value: any): Promise<boolean>;
}

// Interface para repositories com soft delete
export interface ISoftDeleteRepository<T, ID = string> extends IBaseRepository<T, ID> {
	softDelete(id: ID): Promise<void>;
	restore(id: ID): Promise<void>;
	findAllIncludingDeleted(options?: FindAllOptions<T>): Promise<PaginatedResult<T>>;
}
