import { Modu<PERSON>, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserController } from "./controllers/user.controller";
import { CreateUserUseCase } from "./use-cases/create-user/create-user.use-case";
import { FindAllUsersUseCase } from "./use-cases/find-all-users/find-all-users.use-case";
import { FindUserByIdUseCase } from "./use-cases/find-user-by-id/find-user-by-id.use-case";
import { UpdateUserUseCase } from "./use-cases/update-user/update-user.use-case";
import { DeleteUserUseCase } from "./use-cases/delete-user/delete-user.use-case";
import { FindUserByUsernameOrEmailUseCase } from "./use-cases/find-user-by-username-or-email/find-user-by-username-or-email.use-case";
import { UserTypeOrmRepository } from "./repositories/user.repository";
import { User } from "./models/entities/user.entity";
import { PermissionsModule } from "../permissions/permissions.module";

@Module({
	imports: [TypeOrmModule.forFeature([User]), forwardRef(() => PermissionsModule)],
	controllers: [UserController],
	providers: [
		CreateUserUseCase,
		FindAllUsersUseCase,
		FindUserByIdUseCase,
		UpdateUserUseCase,
		DeleteUserUseCase,
		FindUserByUsernameOrEmailUseCase,
		UserTypeOrmRepository,
		{
			provide: "IUserRepository",
			useExisting: UserTypeOrmRepository,
		},
	],
	exports: [
		"IUserRepository",
		CreateUserUseCase,
		FindAllUsersUseCase,
		FindUserByIdUseCase,
		UpdateUserUseCase,
		DeleteUserUseCase,
		FindUserByUsernameOrEmailUseCase,
	],
})
export class UserModule {}
