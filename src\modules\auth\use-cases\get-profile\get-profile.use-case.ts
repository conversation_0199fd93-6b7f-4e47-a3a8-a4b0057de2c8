import { Inject, Injectable } from "@nestjs/common";
import { ICurrentUserPayload } from "../../../../shared/decorators/current-user.decorator";
import { ResourceNotFoundException } from "../../../../shared/exceptions/business.exceptions";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";
import { User } from "../../../user/models/entities/user.entity";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";

@Injectable()
export class GetProfileUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(currentUser: ICurrentUserPayload): Promise<UserResponseDto> {
		// Buscar o usuário atual com dados atualizados
		const user = await this.authRepository.findByUsernameOrEmail(currentUser.username);
		if (!user) {
			throw new ResourceNotFoundException("Usu<PERSON>rio", currentUser.username);
		}

		return this.mapToUserResponseDto(user);
	}

	private mapToUserResponseDto(user: User): UserResponseDto {
		const { password, refreshToken, ...userWithoutSensitiveData } = user;
		return userWithoutSensitiveData as UserResponseDto;
	}
}
