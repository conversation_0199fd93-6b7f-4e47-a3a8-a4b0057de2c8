import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException } from "src/shared/exceptions/business.exceptions";
import { CreateTagDto } from "../../models/dtos";
import { ITag, ITagRepository } from "../../models/interfaces";

@Injectable()
export class CreateTagUseCase {
	constructor(
		@Inject("ITagRepository")
		private tagRepository: ITagRepository
	) {}

	async execute(createTagDto: CreateTagDto): Promise<ITag> {
		// Verificar se já existe uma tag com o mesmo nome
		const existingTag = await this.tagRepository.findByName(createTagDto.name);

		if (existingTag) {
			throw new DuplicateResourceException("Tag", "name", createTagDto.name);
		}

		const tag = {
			name: createTagDto.name,
			color: createTagDto.color,
			description: createTagDto.description,
			worksCount: 0,
		};

		return await this.tagRepository.create(tag);
	}
}
