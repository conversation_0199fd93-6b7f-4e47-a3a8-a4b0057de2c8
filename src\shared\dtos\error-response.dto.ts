import { ApiProperty } from "@nestjs/swagger";

export class ErrorResponseDto {
	@ApiProperty({
		description: "Código de status HTTP",
		example: 400,
		type: "number",
	})
	statusCode: number;

	@ApiProperty({
		description: "Mensagem de erro",
		example: "Dados inválidos fornecidos",
		type: "string",
	})
	message: string | string[];

	@ApiProperty({
		description: "Tipo do erro",
		example: "Bad Request",
		type: "string",
	})
	error: string;

	@ApiProperty({
		description: "Timestamp do erro",
		example: "2024-06-12T10:30:00.000Z",
		type: "string",
		format: "date-time",
	})
	timestamp: string;

	@ApiProperty({
		description: "Caminho da requisição que gerou o erro",
		example: "/api/auth/login",
		type: "string",
	})
	path: string;
}

export class ValidationErrorResponseDto extends ErrorResponseDto {
	@ApiProperty({
		description: "Lista detalhada de erros de validação",
		example: ["username deve ter pelo menos 3 caracteres", "email deve ser um email válido", "password deve ter pelo menos 6 caracteres"],
		type: [String],
	})
	message: string[];
}

export class ConflictErrorResponseDto extends ErrorResponseDto {
	@ApiProperty({
		description: "Mensagem de conflito",
		example: "Email ou nome de usuário já está em uso",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "Detalhes do conflito",
		example: {
			field: "email",
			value: "<EMAIL>",
			reason: "Este email já está cadastrado no sistema",
		},
		required: false,
	})
	details?: {
		field: string;
		value: string;
		reason: string;
	};
}

export class UnauthorizedErrorResponseDto extends ErrorResponseDto {
	@ApiProperty({
		description: "Mensagem de erro de autenticação",
		example: "Credenciais inválidas ou token expirado",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "Código do erro de autenticação",
		example: "INVALID_CREDENTIALS",
		enum: ["INVALID_CREDENTIALS", "TOKEN_EXPIRED", "TOKEN_INVALID", "TOKEN_MISSING"],
	})
	code: string;
}

export class NotFoundErrorResponseDto extends ErrorResponseDto {
	@ApiProperty({
		description: "Mensagem de recurso não encontrado",
		example: "Usuário não encontrado",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "ID do recurso que não foi encontrado",
		example: "123",
		type: "string",
		required: false,
	})
	resourceId?: string;

	@ApiProperty({
		description: "Tipo do recurso não encontrado",
		example: "User",
		type: "string",
		required: false,
	})
	resourceType?: string;
}

export class ForbiddenErrorResponseDto extends ErrorResponseDto {
	@ApiProperty({
		description: "Mensagem de acesso negado",
		example: "Você não tem permissão para acessar este recurso",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "Permissão necessária",
		example: "ADMIN_ACCESS",
		type: "string",
		required: false,
	})
	requiredPermission?: string;
}

export class InternalServerErrorResponseDto extends ErrorResponseDto {
	@ApiProperty({
		description: "Mensagem de erro interno",
		example: "Erro interno do servidor. Tente novamente mais tarde.",
		type: "string",
	})
	message: string;

	@ApiProperty({
		description: "ID único do erro para rastreamento",
		example: "err_123456789",
		type: "string",
		required: false,
	})
	errorId?: string;
}
