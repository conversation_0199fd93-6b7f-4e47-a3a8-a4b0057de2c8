import { Inject, Injectable, forwardRef } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { WorkRepository } from "../../works/repositories/work.repository";
import { Review } from "../models/entities/review.entity";
import { IReview, IReviewFilters, IReviewRepository } from "../models/interfaces";

@Injectable()
export class ReviewRepository implements IReviewRepository {
	constructor(
		@InjectRepository(Review)
		private readonly reviewRepository: Repository<Review>,

		@Inject(forwardRef(() => WorkRepository))
		private readonly workRepository: WorkRepository
	) {}

	async create(review: Partial<IReview>): Promise<IReview> {
		const newReview = this.reviewRepository.create(review);
		const savedReview = await this.reviewRepository.save(newReview);
		await this.updateWorkAverageRating(review.workId);
		return savedReview;
	}

	async update(id: string, review: Partial<IReview>): Promise<IReview> {
		await this.reviewRepository.update(id, review);
		const updatedReview = await this.findById(id);
		if (review.rating) {
			await this.updateWorkAverageRating(updatedReview.workId);
		}
		return updatedReview;
	}

	async delete(id: string): Promise<void> {
		const review = await this.findById(id);
		await this.reviewRepository.delete(id);
		await this.updateWorkAverageRating(review.workId);
	}

	async findById(id: string): Promise<IReview> {
		return await this.reviewRepository.findOne({
			where: { id },
		});
	}

	async findAll(filters: IReviewFilters): Promise<IReview[]> {
		const query = this.buildFilterQuery(filters);

		// Ordenação
		if (filters.sort) {
			query.orderBy(`review.${filters.sort}`, filters.order || "DESC");
		} else {
			query.orderBy("review.createdAt", "DESC");
		}

		// Paginação
		if (filters.skip !== undefined) {
			query.skip(filters.skip);
		}

		if (filters.take !== undefined) {
			query.take(filters.take);
		}

		return await query.getMany();
	}

	async findCount(filters: IReviewFilters): Promise<number> {
		const query = this.buildFilterQuery(filters);
		return await query.getCount();
	}

	private buildFilterQuery(filters: IReviewFilters): SelectQueryBuilder<Review> {
		const query = this.reviewRepository.createQueryBuilder("review");

		if (filters.userId) {
			query.andWhere("review.userId = :userId", { userId: filters.userId });
		}

		if (filters.workId) {
			query.andWhere("review.workId = :workId", { workId: filters.workId });
		}

		if (filters.isPublic !== undefined) {
			query.andWhere("review.isPublic = :isPublic", { isPublic: filters.isPublic });
		}

		if (filters.minRating !== undefined) {
			query.andWhere("review.rating >= :minRating", { minRating: filters.minRating });
		}

		if (filters.maxRating !== undefined) {
			query.andWhere("review.rating <= :maxRating", { maxRating: filters.maxRating });
		}

		return query;
	}

	async incrementLikes(id: string): Promise<void> {
		await this.reviewRepository.increment({ id }, "likes", 1);
	}

	async decrementLikes(id: string): Promise<void> {
		await this.reviewRepository.decrement({ id }, "likes", 1);
	}

	async incrementDislikes(id: string): Promise<void> {
		await this.reviewRepository.increment({ id }, "dislikes", 1);
	}

	async decrementDislikes(id: string): Promise<void> {
		await this.reviewRepository.decrement({ id }, "dislikes", 1);
	}

	async findByUserAndWork(userId: string, workId: string): Promise<IReview> {
		return await this.reviewRepository.findOne({
			where: { userId, workId },
		});
	}

	async updateWorkAverageRating(workId: string): Promise<void> {
		// // Calcular média de todas as reviews para esta obra
		// const result = await this.reviewRepository
		// 	.createQueryBuilder("review")
		// 	.select("AVG(review.rating)", "averageRating")
		// 	.addSelect("COUNT(review.id)", "totalReviews")
		// 	.where("review.workId = :workId", { workId })
		// 	.getRawOne();

		await this.workRepository.updateAverageRating(workId);
	}
}
