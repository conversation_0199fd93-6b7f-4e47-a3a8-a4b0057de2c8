import { Modu<PERSON> } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { Auth<PERSON>ontroller } from "./controllers/auth.controller";
import { ValidateUserUseCase } from "./use-cases/validate-user/validate-user.use-case";
import { LoginUserUseCase } from "./use-cases/login-user/login-user.use-case";
import { RegisterUserUseCase } from "./use-cases/register-user/register-user.use-case";
import { RefreshTokenUseCase } from "./use-cases/refresh-token/refresh-token.use-case";
import { ChangePasswordUseCase } from "./use-cases/change-password/change-password.use-case";
import { GetProfileUseCase } from "./use-cases/get-profile/get-profile.use-case";
import { AuthTypeOrmRepository } from "./repositories/auth.repository";
import { JwtStrategy } from "./strategies/jwt.strategy";
import { LocalStrategy } from "./strategies/local.strategy";
import { UserModule } from "../user/user.module";

@Module({
	imports: [
		UserModule,
		PassportModule,
		JwtModule.registerAsync({
			imports: [ConfigModule],
			useFactory: async (configService: ConfigService) => ({
				secret: configService.get<string>("JWT_SECRET"),
				signOptions: { expiresIn: configService.get<string>("JWT_EXPIRES_IN") },
			}),
			inject: [ConfigService],
		}),
	],
	controllers: [AuthController],
	providers: [
		ValidateUserUseCase,
		LoginUserUseCase,
		RegisterUserUseCase,
		RefreshTokenUseCase,
		ChangePasswordUseCase,
		GetProfileUseCase,
		AuthTypeOrmRepository,
		JwtStrategy,
		LocalStrategy,
		{
			provide: "IAuthRepository",
			useExisting: AuthTypeOrmRepository,
		},
	],
	exports: ["IAuthRepository", ValidateUserUseCase, LoginUserUseCase, RegisterUserUseCase, RefreshTokenUseCase, ChangePasswordUseCase, GetProfileUseCase],
})
export class AuthModule {}
