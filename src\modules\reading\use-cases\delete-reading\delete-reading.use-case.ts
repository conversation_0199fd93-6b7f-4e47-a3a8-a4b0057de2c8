import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IUserWorkRepository } from "../../models/interfaces";

@Injectable()
export class DeleteReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string, userWorkId: string): Promise<void> {
		// Verificar se o registro existe e pertence ao usuário
		const userWork = await this.userWorkRepository.findById(userWorkId);
		if (!userWork) {
			throw new ResourceNotFoundException("Registro de leitura", userWorkId);
		}
		if (userWork.userId !== userId) {
			throw new ResourceNotFoundException("Registro de leitura do usuário", `${userWorkId}:${userId}`);
		}

		await this.userWorkRepository.delete(userWorkId);
	}
}
