import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, <PERSON><PERSON>ptional, MaxLength, Matches } from "class-validator";

export class CreateTagDto {
	@ApiProperty({
		description: "Nome da tag",
		example: "A<PERSON>",
		maxLength: 50,
	})
	@IsString()
	@IsNotEmpty()
	@MaxLength(50)
	name: string;

	@ApiProperty({
		description: "Cor da tag em formato hexadecimal",
		example: "#FF5733",
		required: false,
	})
	@IsString()
	@IsOptional()
	@MaxLength(7)
	@Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
		message: "A cor deve estar no formato hexadecimal (ex: #FF5733)",
	})
	color?: string;

	@ApiProperty({
		description: "Descrição da tag (opcional)",
		example: "Obras com muita ação e cenas de combate",
		required: false,
	})
	@IsString()
	@IsOptional()
	description?: string;
}
