import { AppLogger } from "../logger/app-logger.service";

export abstract class BaseUseCase<TInput, TOutput> {
	protected readonly logger: AppLogger;

	constructor(context?: string) {
		this.logger = AppLogger.getInstance(context || this.constructor.name);
	}

	abstract execute(input: TInput): Promise<TOutput>;

	protected logStart(operation: string, input: TInput, userId?: string | number): void {
		this.logger.logBusinessOperation(`${operation} - Started`, input, userId);
	}

	protected logSuccess(operation: string, output: TOutput, userId?: string | number): void {
		this.logger.logBusinessOperation(`${operation} - Success`, output, userId);
	}

	protected logError(operation: string, error: Error, input?: TInput, userId?: string | number): void {
		this.logger.logError(error, error.stack, undefined, {
			operation,
			input,
			userId,
		});
	}

	protected async executeWithLogging(operation: string, input: TInput, executionFn: () => Promise<TOutput>, userId?: string | number): Promise<TOutput> {
		try {
			this.logStart(operation, input, userId);
			const result = await executionFn();
			this.logSuccess(operation, result, userId);
			return result;
		} catch (error) {
			this.logError(operation, error as Error, input, userId);
			throw error;
		}
	}
}
