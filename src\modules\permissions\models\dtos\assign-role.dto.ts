import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsInt, IsUUID, IsOptional, IsDateString, Min } from "class-validator";

/**
 * DTO para atribuir um role a um usuário
 */
export class AssignRoleDto {
	@ApiProperty({
		description: "ID do usuário que receberá o role",
		example: 123,
		minimum: 1,
		examples: {
			user: {
				summary: "Usuário Regular",
				value: 123,
			},
			moderator: {
				summary: "Novo Moderador",
				value: 456,
			},
		},
	})
	@IsNotEmpty({ message: "ID do usuário é obrigatório" })
	@IsInt({ message: "ID do usuário deve ser um número inteiro" })
	@Min(1, { message: "ID do usuário deve ser maior que 0" })
	userId: number;

	@ApiProperty({
		description: "ID do role a ser atribuído",
		example: "uuid-role-123",
		format: "uuid",
		examples: {
			moderator: {
				summary: "Role de Moderador",
				value: "550e8400-e29b-41d4-a716-************",
			},
			admin: {
				summary: "Role de Admin",
				value: "550e8400-e29b-41d4-a716-************",
			},
		},
	})
	@IsNotEmpty({ message: "ID do role é obrigatório" })
	@IsUUID(4, { message: "ID do role deve ser um UUID válido" })
	roleId: string;

	@ApiProperty({
		description: "Data de expiração do role (opcional, para roles temporários)",
		example: "2024-12-31T23:59:59.000Z",
		format: "date-time",
		required: false,
		examples: {
			temporary: {
				summary: "Role Temporário",
				value: "2024-12-31T23:59:59.000Z",
			},
			trial: {
				summary: "Período de Teste",
				value: "2024-07-01T00:00:00.000Z",
			},
		},
	})
	@IsOptional()
	@IsDateString({}, { message: "Data de expiração deve estar em formato ISO válido" })
	expiresAt?: string;
}
