import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsBoolean, IsUUID } from "class-validator";

export class CreateCommentReactionDto {
	@ApiProperty({
		description: "ID do comentário a reagir",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
	})
	@IsUUID()
	@IsNotEmpty()
	commentId: string;

	@ApiProperty({
		description: "Se é uma reação positiva (like) ou negativa (dislike)",
		example: true,
	})
	@IsBoolean()
	@IsNotEmpty()
	isLike: boolean;
}
