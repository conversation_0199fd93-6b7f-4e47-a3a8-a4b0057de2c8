import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsBoolean, IsString } from "class-validator";
import { Transform } from "class-transformer";

export class RankingFiltersDto {
	@ApiPropertyOptional({
		description: "Filtrar por rankings públicos ou privados",
		example: "true",
		required: false,
	})
	@IsOptional()
	@Transform(({ value }) => value === "true")
	@IsBoolean()
	isPublic?: boolean;

	@ApiPropertyOptional({
		description: "Busca por nome do ranking",
		example: "Top 10",
		required: false,
	})
	@IsOptional()
	@IsString()
	search?: string;
}
