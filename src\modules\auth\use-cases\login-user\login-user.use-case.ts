import { Inject, Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { DatabaseException, InvalidCredentialsException } from "../../../../shared/exceptions/business.exceptions";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";
import { User } from "../../../user/models/entities/user.entity";
import { AuthResponseDto } from "../../models/dtos/auth-response.dto";
import { LoginDto } from "../../models/dtos/login.dto";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";
import { ValidateUserUseCase } from "../validate-user/validate-user.use-case";

@Injectable()
export class LoginUserUseCase {
	constructor(
		private readonly validateUserUseCase: ValidateUserUseCase,
		private readonly jwtService: JwtService,
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(loginDto: LoginDto): Promise<AuthResponseDto> {
		try {
			const user = await this.validateUserUseCase.execute(loginDto.usernameOrEmail, loginDto.password);
			if (!user) {
				throw new InvalidCredentialsException();
			}

			// Gerar tokens
			const accessTokenPayload = { username: user.username, sub: user.id, email: user.email };
			const refreshTokenPayload = { username: user.username, sub: user.id, type: "refresh" };

			const access_token = this.jwtService.sign(accessTokenPayload);
			const refresh_token = this.jwtService.sign(refreshTokenPayload, { expiresIn: "30d" });

			// Salvar refresh token no banco
			await this.authRepository.updateRefreshToken(user.id, refresh_token);

			return {
				access_token,
				refresh_token,
				token_type: "Bearer",
				expires_in: 604800, // 7 dias
				user: this.mapToUserResponseDto(user),
			};
		} catch (error) {
			if (error instanceof InvalidCredentialsException) {
				throw error;
			}
			throw new DatabaseException("processo de login", error);
		}
	}

	private mapToUserResponseDto(user: Partial<User>): UserResponseDto {
		return {
			id: user.id,
			username: user.username,
			email: user.email,
			fullName: user.fullName,
			avatar: user.avatar,
			isActive: user.isActive,
			role: user.role,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
		};
	}
}
