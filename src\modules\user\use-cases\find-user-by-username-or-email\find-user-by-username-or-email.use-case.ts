import { Injectable, Inject } from "@nestjs/common";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";
import { User } from "../../models/entities/user.entity";

@Injectable()
export class FindUserByUsernameOrEmailUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(usernameOrEmail: string): Promise<User | null> {
		return this.userRepository.findByUsernameOrEmail(usernameOrEmail);
	}
}
