import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { UserRole } from "../../modules/user/models/enums";

export interface ICurrentUserPayload {
	id: number;
	username: string;
	email: string;
	role: UserRole;
	isActive: boolean;
}

export const CurrentUser = createParamDecorator((data: unknown, ctx: ExecutionContext): ICurrentUserPayload => {
	const request = ctx.switchToHttp().getRequest();
	return request.user;
});
