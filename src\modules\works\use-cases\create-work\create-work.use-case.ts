import { Injectable } from "@nestjs/common";
import { DuplicateResourceException } from "src/shared/exceptions/business.exceptions";
import { ICreateWorkRequest, IWork } from "../../models/interfaces";
import { WorkRepository } from "../../repositories";

@Injectable()
export class CreateWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(data: ICreateWorkRequest): Promise<IWork> {
		// Verificar se já existe uma obra com o mesmo título
		const existingWork = await this.workRepository.findByTitle(data.title);
		if (existingWork) {
			throw new DuplicateResourceException("Obra", "title", data.title);
		}

		return await this.workRepository.create(data);
	}
}
