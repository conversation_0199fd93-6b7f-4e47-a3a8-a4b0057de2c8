import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IListRepository } from "../../models/interfaces";

@Injectable()
export class DeleteListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: string, listId: string): Promise<void> {
		// Verificar se a lista existe e pertence ao usuário
		const existingList = await this.listRepository.findById(listId);
		if (!existingList) {
			throw new ResourceNotFoundException("Lista", listId);
		}
		if (existingList.userId !== userId) {
			throw new ResourceNotFoundException("Lista do usuário", `${listId}:${userId}`);
		}

		await this.listRepository.delete(listId);
	}
}
