services:
    postgres:
        image: postgres:15
        container_name: backend-test-db
        restart: always
        environment:
            POSTGRES_DB: mb_base
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: postgres
            PGDATA: /var/lib/postgresql/data/pgdata
        ports:
            - "5432:5432"
        volumes:
            - postgres_data:/var/lib/postgresql/data
            - ./init.sql:/docker-entrypoint-initdb.d/init.sql
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U postgres -d mb_base"]
            interval: 10s
            timeout: 5s
            retries: 5

    pgadmin:
        image: dpage/pgadmin4
        container_name: backend-test-pgadmin
        restart: always
        environment:
            PGADMIN_DEFAULT_EMAIL: <EMAIL>
            PGADMIN_DEFAULT_PASSWORD: admin123
        ports:
            - "8080:80"
        depends_on:
            - postgres

volumes:
    postgres_data:
