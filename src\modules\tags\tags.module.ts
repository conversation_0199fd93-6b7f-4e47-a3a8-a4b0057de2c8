import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Tag, WorkTag } from "./models/entities";
import { TagRepository, WorkTagRepository } from "./repositories";
import {
	CreateTagUseCase,
	UpdateTagUseCase,
	DeleteTagUseCase,
	GetTagUseCase,
	ListTagsUseCase,
	GetPopularTagsUseCase,
	GetWorkTagsUseCase,
	GetWorksByTagUseCase,
	AddTagToWorkUseCase,
	RemoveTagFromWorkUseCase,
} from "./use-cases";
import { TagsController } from "./controllers";
import { WorksModule } from "../works/works.module";

@Module({
	imports: [
		TypeOrmModule.forFeature([Tag, WorkTag]),
		forwardRef(() => WorksModule), // Para ter acesso ao WorkRepository
	],
	controllers: [TagsController],
	providers: [
		TagRepository,
		WorkTagRepository,
		CreateTagUseCase,
		UpdateTagUseCase,
		DeleteTagUseCase,
		GetTagUseCase,
		ListTagsUseCase,
		GetPopularTagsUseCase,
		GetWorkTagsUseCase,
		GetWorksByTagUseCase,
		AddTagToWorkUseCase,
		RemoveTagFromWorkUseCase,
		{
			provide: "ITagRepository",
			useExisting: TagRepository,
		},
		{
			provide: "IWorkTagRepository",
			useExisting: WorkTagRepository,
		},
	],
	exports: [
		"ITagRepository",
		"IWorkTagRepository",
		TagRepository,
		WorkTagRepository,
		CreateTagUseCase,
		UpdateTagUseCase,
		DeleteTagUseCase,
		GetTagUseCase,
		ListTagsUseCase,
		GetPopularTagsUseCase,
		GetWorkTagsUseCase,
		GetWorksByTagUseCase,
		AddTagToWorkUseCase,
		RemoveTagFromWorkUseCase,
	],
})
export class TagsModule {}
