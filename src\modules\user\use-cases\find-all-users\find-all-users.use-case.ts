import { Inject, Injectable } from "@nestjs/common";
import { UserResponseDto } from "../../models/dtos/user-response.dto";
import { User } from "../../models/entities/user.entity";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";

@Injectable()
export class FindAllUsersUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(): Promise<UserResponseDto[]> {
		const users = await this.userRepository.findAll();
		return users.map(user => this.mapToResponseDto(user));
	}

	private mapToResponseDto(user: User): UserResponseDto {
		const { password, ...userWithoutPassword } = user;
		return userWithoutPassword as UserResponseDto;
	}
}
