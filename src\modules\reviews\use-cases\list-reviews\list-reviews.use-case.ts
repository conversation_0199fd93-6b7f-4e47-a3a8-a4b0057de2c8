import { Inject, Injectable } from "@nestjs/common";
import { ReviewFiltersDto } from "../../models/dtos";
import { IReview, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class ListReviewsUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(filters: ReviewFiltersDto, currentUserId?: string): Promise<{ data: IReview[]; total: number }> {
		// Se não for o próprio usuário buscando, restringir apenas a reviews públicas
		if (filters.userId !== currentUserId) {
			filters.isPublic = true;
		}

		const [reviews, total] = await Promise.all([this.reviewRepository.findAll(filters), this.reviewRepository.findCount(filters)]);

		return { data: reviews, total };
	}
}
