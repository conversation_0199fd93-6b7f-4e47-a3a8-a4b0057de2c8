import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IRankingRepository } from "../../models/interfaces";

@Injectable()
export class DeleteRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(id: string, userId: string): Promise<void> {
		const existingRanking = await this.rankingRepository.findById(id);
		if (!existingRanking) {
			throw new ResourceNotFoundException("Ranking", id);
		}
		// Verificar se o ranking pertence ao usuário
		if (existingRanking.userId !== userId) {
			throw new ResourceNotFoundException("Ranking do usuário", `${id}:${userId}`);
		}

		await this.rankingRepository.delete(id);
	}
}
