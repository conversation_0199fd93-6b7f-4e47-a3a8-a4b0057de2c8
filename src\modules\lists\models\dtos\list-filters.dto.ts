import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsBoolean, IsString, IsInt, IsIn, Min } from "class-validator";
import { Transform } from "class-transformer";

export class ListFiltersDto {
	@ApiPropertyOptional({
		description: "Filtrar apenas listas públicas",
		example: true,
	})
	@IsOptional()
	@Transform(({ value }) => value === "true" || value === true)
	@IsBoolean()
	isPublic?: boolean;

	@ApiPropertyOptional({
		description: "Busca por nome ou descrição",
		example: "favoritos",
	})
	@IsOptional()
	@IsString()
	search?: string;

	@ApiPropertyOptional({
		description: "Número da página",
		example: 1,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsInt()
	@Min(1)
	page?: number;

	@ApiPropertyOptional({
		description: "Itens por página",
		example: 20,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsInt()
	@Min(1)
	limit?: number;

	@ApiPropertyOptional({
		description: "Campo para ordenação",
		example: "createdAt",
		enum: ["name", "createdAt", "updatedAt", "itemsCount"],
	})
	@IsOptional()
	@IsIn(["name", "createdAt", "updatedAt", "itemsCount"])
	sortBy?: "name" | "createdAt" | "updatedAt" | "itemsCount";

	@ApiPropertyOptional({
		description: "Direção da ordenação",
		example: "DESC",
		enum: ["ASC", "DESC"],
	})
	@IsOptional()
	@IsIn(["ASC", "DESC"])
	sortOrder?: "ASC" | "DESC";
}
