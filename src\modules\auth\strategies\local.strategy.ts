import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { Strategy } from "passport-local";
import { ValidateUserUseCase } from "../use-cases/validate-user/validate-user.use-case";

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
	constructor(private validateUserUseCase: ValidateUserUseCase) {
		super({ usernameField: "usernameOrEmail" });
	}

	async validate(usernameOrEmail: string, password: string): Promise<any> {
		const user = await this.validateUserUseCase.execute(usernameOrEmail, password);
		if (!user) {
			throw new UnauthorizedException("Credenciais inválidas");
		}
		return user;
	}
}
