import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import {
	ApiBearerAuth,
	ApiConflictResponse,
	ApiInternalServerErrorResponse,
	ApiNotFoundResponse,
	ApiOperation,
	ApiParam,
	ApiResponse,
	ApiTags,
	ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { CurrentUser, ICurrentUserPayload } from "../../../shared/decorators/current-user.decorator";
import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";

import {
	CreateCommentReactionUseCase,
	CreateReviewCommentUseCase,
	CreateReviewReactionUseCase,
	CreateReviewUseCase,
	DeleteCommentReactionUseCase,
	DeleteReviewCommentUseCase,
	DeleteReviewReactionUseCase,
	DeleteReviewUseCase,
	GetReviewCommentsUseCase,
	GetReviewUseCase,
	ListReviewsUseCase,
	UpdateReviewCommentUseCase,
	UpdateReviewUseCase,
} from "../use-cases";

import {
	CreateCommentReactionDto,
	CreateReviewCommentDto,
	CreateReviewDto,
	CreateReviewReactionDto,
	ReviewCommentFiltersDto,
	ReviewFiltersDto,
	UpdateReviewCommentDto,
	UpdateReviewDto,
} from "../models/dtos";

import { ErrorResponseDto } from "src/shared/dtos/error-response.dto";
import { DeleteSuccessResponseDto, SuccessResponseDto } from "src/shared/dtos/success-response.dto";
import { ICommentReaction, IReview, IReviewComment, IReviewReaction } from "../models/interfaces";

@ApiTags("Reviews")
@Controller("reviews")
export class ReviewsController {
	constructor(
		private readonly createReviewUseCase: CreateReviewUseCase,
		private readonly updateReviewUseCase: UpdateReviewUseCase,
		private readonly deleteReviewUseCase: DeleteReviewUseCase,
		private readonly getReviewUseCase: GetReviewUseCase,
		private readonly listReviewsUseCase: ListReviewsUseCase,
		private readonly createReviewCommentUseCase: CreateReviewCommentUseCase,
		private readonly updateReviewCommentUseCase: UpdateReviewCommentUseCase,
		private readonly deleteReviewCommentUseCase: DeleteReviewCommentUseCase,
		private readonly getReviewCommentsUseCase: GetReviewCommentsUseCase,
		private readonly createReviewReactionUseCase: CreateReviewReactionUseCase,
		private readonly deleteReviewReactionUseCase: DeleteReviewReactionUseCase,
		private readonly createCommentReactionUseCase: CreateCommentReactionUseCase,
		private readonly deleteCommentReactionUseCase: DeleteCommentReactionUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Criar uma nova avaliação" })
	@ApiResponse({ status: 201, description: "Avaliação criada com sucesso", type: SuccessResponseDto })
	@ApiConflictResponse({ description: "Você já tem uma avaliação para esta obra", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async createReview(@CurrentUser() user: ICurrentUserPayload, @Body() createReviewDto: CreateReviewDto): Promise<SuccessResponseDto<IReview>> {
		const review = await this.createReviewUseCase.execute(String(user.id), createReviewDto);
		const response = new SuccessResponseDto<IReview>();
		response.success = true;
		response.message = "Avaliação criada com sucesso";
		response.data = review;
		return response;
	}

	@Patch(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Atualizar uma avaliação" })
	@ApiParam({ name: "id", description: "ID da avaliação" })
	@ApiResponse({ status: 200, description: "Avaliação atualizada com sucesso", type: SuccessResponseDto })
	@ApiNotFoundResponse({ description: "Avaliação não encontrada", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async updateReview(
		@CurrentUser() user: ICurrentUserPayload,
		@Param("id") id: string,
		@Body() updateReviewDto: UpdateReviewDto
	): Promise<SuccessResponseDto<IReview>> {
		const review = await this.updateReviewUseCase.execute(id, String(user.id), updateReviewDto);
		const response = new SuccessResponseDto<IReview>();
		response.success = true;
		response.message = "Avaliação atualizada com sucesso";
		response.data = review;
		return response;
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Excluir uma avaliação" })
	@ApiParam({ name: "id", description: "ID da avaliação" })
	@ApiResponse({ status: 200, description: "Avaliação excluída com sucesso", type: DeleteSuccessResponseDto })
	@ApiNotFoundResponse({ description: "Avaliação não encontrada", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async deleteReview(@CurrentUser() user: ICurrentUserPayload, @Param("id") id: string): Promise<DeleteSuccessResponseDto> {
		await this.deleteReviewUseCase.execute(id, String(user.id));
		const response = new DeleteSuccessResponseDto();
		response.success = true;
		response.message = "Avaliação excluída com sucesso";
		response.deletedId = id;
		response.deletedAt = new Date().toISOString();
		return response;
	}

	@Get(":id")
	@ApiOperation({ summary: "Obter uma avaliação pelo ID" })
	@ApiParam({ name: "id", description: "ID da avaliação" })
	@ApiResponse({ status: 200, description: "Avaliação encontrada", type: SuccessResponseDto })
	@ApiNotFoundResponse({ description: "Avaliação não encontrada", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async getReview(@Param("id") id: string, @CurrentUser() user?: ICurrentUserPayload): Promise<SuccessResponseDto<IReview>> {
		const review = await this.getReviewUseCase.execute(id, user ? String(user.id) : undefined);
		const response = new SuccessResponseDto<IReview>();
		response.success = true;
		response.message = "Avaliação encontrada";
		response.data = review;
		return response;
	}

	@Get()
	@ApiOperation({ summary: "Listar avaliações" })
	@ApiResponse({ status: 200, description: "Lista de avaliações", type: SuccessResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async listReviews(
		@Query() filters: ReviewFiltersDto,
		@CurrentUser() user?: ICurrentUserPayload
	): Promise<SuccessResponseDto<{ data: IReview[]; total: number }>> {
		const result = await this.listReviewsUseCase.execute(filters, user ? String(user.id) : undefined);
		const response = new SuccessResponseDto<{ data: IReview[]; total: number }>();
		response.success = true;
		response.message = "Lista de avaliações";
		response.data = result;
		return response;
	}

	@Post("comments")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Criar um novo comentário em uma avaliação" })
	@ApiResponse({ status: 201, description: "Comentário criado com sucesso", type: SuccessResponseDto })
	@ApiNotFoundResponse({ description: "Avaliação não encontrada", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async createComment(
		@CurrentUser() user: ICurrentUserPayload,
		@Body() createCommentDto: CreateReviewCommentDto
	): Promise<SuccessResponseDto<IReviewComment>> {
		const comment = await this.createReviewCommentUseCase.execute(String(user.id), createCommentDto);
		const response = new SuccessResponseDto<IReviewComment>();
		response.success = true;
		response.message = "Comentário criado com sucesso";
		response.data = comment;
		return response;
	}

	@Patch("comments/:id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Atualizar um comentário" })
	@ApiParam({ name: "id", description: "ID do comentário" })
	@ApiResponse({ status: 200, description: "Comentário atualizado com sucesso", type: SuccessResponseDto })
	@ApiNotFoundResponse({ description: "Comentário não encontrado", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async updateComment(
		@CurrentUser() user: ICurrentUserPayload,
		@Param("id") id: string,
		@Body() updateCommentDto: UpdateReviewCommentDto
	): Promise<SuccessResponseDto<IReviewComment>> {
		const comment = await this.updateReviewCommentUseCase.execute(id, String(user.id), updateCommentDto);
		const response = new SuccessResponseDto<IReviewComment>();
		response.success = true;
		response.message = "Comentário atualizado com sucesso";
		response.data = comment;
		return response;
	}

	@Delete("comments/:id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Excluir um comentário" })
	@ApiParam({ name: "id", description: "ID do comentário" })
	@ApiResponse({ status: 200, description: "Comentário excluído com sucesso", type: DeleteSuccessResponseDto })
	@ApiNotFoundResponse({ description: "Comentário não encontrado", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async deleteComment(@CurrentUser() user: ICurrentUserPayload, @Param("id") id: string): Promise<DeleteSuccessResponseDto> {
		await this.deleteReviewCommentUseCase.execute(id, String(user.id));
		const response = new DeleteSuccessResponseDto();
		response.success = true;
		response.message = "Comentário excluído com sucesso";
		response.deletedId = id;
		response.deletedAt = new Date().toISOString();
		return response;
	}

	@Get(":reviewId/comments")
	@ApiOperation({ summary: "Listar comentários de uma avaliação" })
	@ApiParam({ name: "reviewId", description: "ID da avaliação" })
	@ApiResponse({ status: 200, description: "Lista de comentários", type: SuccessResponseDto })
	@ApiNotFoundResponse({ description: "Avaliação não encontrada", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async getComments(
		@Param("reviewId") reviewId: string,
		@Query() filters: ReviewCommentFiltersDto,
		@CurrentUser() user?: ICurrentUserPayload
	): Promise<SuccessResponseDto<{ data: IReviewComment[]; total: number }>> {
		const result = await this.getReviewCommentsUseCase.execute(reviewId, filters, user ? String(user.id) : undefined);
		const response = new SuccessResponseDto<{ data: IReviewComment[]; total: number }>();
		response.success = true;
		response.message = "Lista de comentários";
		response.data = result;
		return response;
	}

	@Post("reactions")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Reagir a uma avaliação (like/dislike)" })
	@ApiResponse({ status: 201, description: "Reação criada com sucesso", type: SuccessResponseDto })
	@ApiConflictResponse({ description: "Você já reagiu a esta avaliação", type: ErrorResponseDto })
	@ApiNotFoundResponse({ description: "Avaliação não encontrada", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async createReviewReaction(
		@CurrentUser() user: ICurrentUserPayload,
		@Body() createReactionDto: CreateReviewReactionDto
	): Promise<SuccessResponseDto<IReviewReaction>> {
		const reaction = await this.createReviewReactionUseCase.execute(String(user.id), createReactionDto);
		const response = new SuccessResponseDto<IReviewReaction>();
		response.success = true;
		response.message = "Reação criada com sucesso";
		response.data = reaction;
		return response;
	}

	@Delete("reactions/:reviewId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Remover reação de uma avaliação" })
	@ApiParam({ name: "reviewId", description: "ID da avaliação" })
	@ApiResponse({ status: 200, description: "Reação removida com sucesso", type: DeleteSuccessResponseDto })
	@ApiNotFoundResponse({ description: "Reação não encontrada", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async deleteReviewReaction(@CurrentUser() user: ICurrentUserPayload, @Param("reviewId") reviewId: string): Promise<DeleteSuccessResponseDto> {
		await this.deleteReviewReactionUseCase.execute(reviewId, String(user.id));
		const response = new DeleteSuccessResponseDto();
		response.success = true;
		response.message = "Reação removida com sucesso";
		response.deletedId = reviewId;
		response.deletedAt = new Date().toISOString();
		return response;
	}

	@Post("comments/reactions")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Reagir a um comentário (like/dislike)" })
	@ApiResponse({ status: 201, description: "Reação criada com sucesso", type: SuccessResponseDto })
	@ApiConflictResponse({ description: "Você já reagiu a este comentário", type: ErrorResponseDto })
	@ApiNotFoundResponse({ description: "Comentário não encontrado", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async createCommentReaction(
		@CurrentUser() user: ICurrentUserPayload,
		@Body() createReactionDto: CreateCommentReactionDto
	): Promise<SuccessResponseDto<ICommentReaction>> {
		const reaction = await this.createCommentReactionUseCase.execute(String(user.id), createReactionDto);
		const response = new SuccessResponseDto<ICommentReaction>();
		response.success = true;
		response.message = "Reação criada com sucesso";
		response.data = reaction;
		return response;
	}

	@Delete("comments/reactions/:commentId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Remover reação de um comentário" })
	@ApiParam({ name: "commentId", description: "ID do comentário" })
	@ApiResponse({ status: 200, description: "Reação removida com sucesso", type: DeleteSuccessResponseDto })
	@ApiNotFoundResponse({ description: "Reação não encontrada", type: ErrorResponseDto })
	@ApiUnauthorizedResponse({ description: "Não autorizado", type: ErrorResponseDto })
	@ApiInternalServerErrorResponse({ description: "Erro interno do servidor", type: ErrorResponseDto })
	async deleteCommentReaction(@CurrentUser() user: ICurrentUserPayload, @Param("commentId") commentId: string): Promise<DeleteSuccessResponseDto> {
		await this.deleteCommentReactionUseCase.execute(commentId, String(user.id));
		const response = new DeleteSuccessResponseDto();
		response.success = true;
		response.message = "Reação removida com sucesso";
		response.deletedId = commentId;
		response.deletedAt = new Date().toISOString();
		return response;
	}
}
