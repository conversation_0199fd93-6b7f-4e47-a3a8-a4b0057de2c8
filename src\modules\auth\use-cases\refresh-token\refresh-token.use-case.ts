import { Inject, Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { DatabaseException, InvalidTokenException, ResourceNotFoundException } from "../../../../shared/exceptions/business.exceptions";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";
import { User } from "../../../user/models/entities/user.entity";
import { AuthResponseDto } from "../../models/dtos/auth-response.dto";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";

@Injectable()
export class RefreshTokenUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository,
		private readonly jwtService: JwtService
	) {}

	async execute(refreshToken: string): Promise<AuthResponseDto> {
		try {
			// Verificar se o refresh token é válido
			const payload = this.jwtService.verify(refreshToken);

			if (payload.type !== "refresh") {
				throw new InvalidTokenException("refresh", "Tipo de token inválido");
			}

			// Buscar o usuário pelo ID do payload
			const user = await this.authRepository.findByUsernameOrEmail(payload.username);
			if (!user) {
				throw new ResourceNotFoundException("Usuário", payload.username);
			}

			// Verificar se o refresh token armazenado no banco é o mesmo
			if (user.refreshToken !== refreshToken) {
				throw new InvalidTokenException("refresh", "Token expirado ou já utilizado");
			}

			// Gerar novos tokens
			const accessTokenPayload = { username: user.username, sub: user.id, email: user.email };
			const refreshTokenPayload = { username: user.username, sub: user.id, type: "refresh" };

			const access_token = this.jwtService.sign(accessTokenPayload);
			const refresh_token = this.jwtService.sign(refreshTokenPayload, { expiresIn: "30d" });

			// Atualizar o refresh token no banco
			await this.authRepository.updateRefreshToken(user.id, refresh_token);

			return {
				access_token,
				refresh_token,
				token_type: "Bearer",
				expires_in: 604800, // 7 dias
				user: this.mapToUserResponseDto(user),
			};
		} catch (error) {
			if (error instanceof InvalidTokenException || error instanceof ResourceNotFoundException) {
				throw error;
			}
			throw new DatabaseException("renovação de token", error);
		}
	}

	private mapToUserResponseDto(user: User): UserResponseDto {
		const { password, refreshToken, ...userWithoutSensitiveData } = user;
		return userWithoutSensitiveData as UserResponseDto;
	}
}
