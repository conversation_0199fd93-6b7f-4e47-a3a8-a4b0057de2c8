import { IWorkTag } from "./IWorkTag.interface";

export interface IWorkTagFilters {
	tagIds?: string[];
}

export interface IWorkTagRepository {
	create(workTag: Partial<IWorkTag>): Promise<IWorkTag>;
	delete(id: string): Promise<void>;
	deleteByWorkAndTag(workId: string, tagId: string): Promise<void>;
	findById(id: string): Promise<IWorkTag>;
	findByWorkId(workId: string): Promise<IWorkTag[]>;
	findByTagId(tagId: string): Promise<IWorkTag[]>;
	findByWorkIdAndTagId(workId: string, tagId: string): Promise<IWorkTag>;
	findWorksByTags(tagIds: string[]): Promise<string[]>;
}
