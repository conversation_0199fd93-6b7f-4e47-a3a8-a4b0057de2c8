import { ApiProperty } from "@nestjs/swagger";
import { UserRole } from "../enums";

export class UserResponseDto {
	@ApiProperty({
		description: "Identificador único do usuário no sistema",
		example: 1,
		type: "integer",
		minimum: 1,
	})
	id: number;

	@ApiProperty({
		description: "Nome de usuário único usado para login",
		example: "joao_silva",
		type: "string",
		minLength: 3,
		maxLength: 50,
		pattern: "^[a-zA-Z0-9_.-]+$",
	})
	username: string;

	@ApiProperty({
		description: "Endereço de email único do usuário",
		example: "<EMAIL>",
		type: "string",
		format: "email",
	})
	email: string;

	@ApiProperty({
		description: "Nome completo do usuário",
		example: "João Silva",
		type: "string",
		maxLength: 100,
		required: false,
		nullable: true,
	})
	fullName?: string;

	@ApiProperty({
		description: "URL da imagem de avatar do usuário",
		example: "https://example.com/avatars/joao.jpg",
		type: "string",
		format: "uri",
		required: false,
		nullable: true,
	})
	avatar?: string;

	@ApiProperty({
		description: "Status de ativação do usuário",
		example: true,
		type: "boolean",
		default: true,
	})
	isActive: boolean;

	@ApiProperty({
		description: "Role/função do usuário no sistema",
		example: UserRole.USER,
		enum: UserRole,
		enumName: "UserRole",
		default: UserRole.USER,
	})
	role: UserRole;

	@ApiProperty({
		description: "Data e hora de criação da conta",
		example: "2024-06-10T08:00:00.000Z",
		type: "string",
		format: "date-time",
	})
	createdAt: Date;

	@ApiProperty({
		description: "Data e hora da última atualização dos dados",
		example: "2024-06-12T10:30:00.000Z",
		type: "string",
		format: "date-time",
	})
	updatedAt: Date;
}
