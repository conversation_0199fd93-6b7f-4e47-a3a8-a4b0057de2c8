import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsOptional, IsBoolean, IsUrl, <PERSON><PERSON>ength, Min<PERSON>ength } from "class-validator";

export class UpdateListDto {
	@ApiPropertyOptional({
		description: "Nome da lista",
		example: "Meus Favoritos Atualizados",
		minLength: 1,
		maxLength: 100,
	})
	@IsOptional()
	@IsString()
	@MinLength(1)
	@MaxLength(100)
	name?: string;

	@ApiPropertyOptional({
		description: "Descrição da lista",
		example: "Minhas obras favoritas atualizadas",
	})
	@IsOptional()
	@IsString()
	@MaxLength(500)
	description?: string;

	@ApiPropertyOptional({
		description: "Se a lista é pública",
		example: true,
	})
	@IsOptional()
	@IsBoolean()
	isPublic?: boolean;

	@ApiPropertyOptional({
		description: "URL da imagem de capa da lista",
		example: "https://example.com/new-cover.jpg",
	})
	@IsOptional()
	@IsUrl()
	coverImage?: string;
}
