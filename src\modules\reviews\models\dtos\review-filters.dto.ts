import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString, IsUUI<PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class ReviewFiltersDto {
	@ApiProperty({
		description: "Filtrar por ID do usuário",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
		required: false,
	})
	@IsUUID()
	@IsOptional()
	userId?: string;

	@ApiProperty({
		description: "Filtrar por ID da obra",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
		required: false,
	})
	@IsUUID()
	@IsOptional()
	workId?: string;

	@ApiProperty({
		description: "Filtrar por visibilidade (públicas ou privadas)",
		example: true,
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	isPublic?: boolean;

	@ApiProperty({
		description: "Filtrar por nota mínima",
		example: 7,
		required: false,
	})
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Max(10)
	@Type(() => Number)
	minRating?: number;

	@ApiProperty({
		description: "Filtrar por nota máxima",
		example: 10,
		required: false,
	})
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Max(10)
	@Type(() => Number)
	maxRating?: number;

	@ApiProperty({
		description: "Quantidade de itens a pular",
		example: 0,
		required: false,
		default: 0,
	})
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Type(() => Number)
	skip?: number;

	@ApiProperty({
		description: "Quantidade de itens a retornar",
		example: 10,
		required: false,
		default: 10,
	})
	@IsNumber()
	@IsOptional()
	@Min(1)
	@Type(() => Number)
	take?: number;

	@ApiProperty({
		description: "Campo para ordenação",
		example: "createdAt",
		required: false,
		default: "createdAt",
	})
	@IsString()
	@IsOptional()
	sort?: string;

	@ApiProperty({
		description: "Ordem da ordenação",
		example: "DESC",
		required: false,
		enum: ["ASC", "DESC"],
		default: "DESC",
	})
	@IsEnum(["ASC", "DESC"])
	@IsOptional()
	order?: "ASC" | "DESC";
}
