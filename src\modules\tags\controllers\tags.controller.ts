import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import {
	ApiBearerAuth,
	ApiBody,
	ApiConflictResponse,
	ApiExtraModels,
	ApiInternalServerErrorResponse,
	ApiNotFoundResponse,
	ApiOperation,
	ApiParam,
	ApiQuery,
	ApiResponse,
	ApiTags,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import {
	CreateTagUseCase,
	UpdateTagUseCase,
	DeleteTagUseCase,
	GetTagUseCase,
	ListTagsUseCase,
	GetPopularTagsUseCase,
	AddTagToWorkUseCase,
	RemoveTagFromWorkUseCase,
	GetWorkTagsUseCase,
	GetWorksByTagUseCase,
} from "../use-cases";
import { CreateTagDto, UpdateTagDto, TagFiltersDto } from "../models/dtos";
import {
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";

@ApiTags("tags")
@ApiExtraModels(
	CreateTagDto,
	UpdateTagDto,
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto
)
@Controller("tags")
export class TagsController {
	constructor(
		private readonly createTagUseCase: CreateTagUseCase,
		private readonly updateTagUseCase: UpdateTagUseCase,
		private readonly deleteTagUseCase: DeleteTagUseCase,
		private readonly getTagUseCase: GetTagUseCase,
		private readonly listTagsUseCase: ListTagsUseCase,
		private readonly getPopularTagsUseCase: GetPopularTagsUseCase,
		private readonly addTagToWorkUseCase: AddTagToWorkUseCase,
		private readonly removeTagFromWorkUseCase: RemoveTagFromWorkUseCase,
		private readonly getWorkTagsUseCase: GetWorkTagsUseCase,
		private readonly getWorksByTagUseCase: GetWorksByTagUseCase
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Criar nova tag",
		description: "Cria uma nova tag para categorização de obras",
	})
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: "Tag criada com sucesso",
	})
	@ApiConflictResponse({
		description: "Já existe uma tag com esse nome",
		type: ConflictErrorResponseDto,
	})
	async createTag(@Body() createTagDto: CreateTagDto) {
		return await this.createTagUseCase.execute(createTagDto);
	}

	@Get()
	@ApiOperation({
		summary: "Listar todas as tags",
		description: "Lista todas as tags disponíveis no sistema com opções de filtro",
	})
	async listTags(@Query() filters: TagFiltersDto) {
		return await this.listTagsUseCase.execute(filters);
	}

	@Get("popular")
	@ApiOperation({
		summary: "Listar tags populares",
		description: "Lista as tags mais utilizadas no sistema",
	})
	@ApiQuery({
		name: "limit",
		description: "Limite de tags a serem retornadas",
		required: false,
		type: Number,
	})
	async getPopularTags(@Query("limit") limit: number = 10) {
		return await this.getPopularTagsUseCase.execute(limit);
	}

	@Get(":id")
	@ApiOperation({
		summary: "Obter tag específica",
		description: "Retorna os detalhes de uma tag específica",
	})
	@ApiParam({
		name: "id",
		description: "ID da tag",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiNotFoundResponse({
		description: "Tag não encontrada",
		type: NotFoundErrorResponseDto,
	})
	async getTag(@Param("id") id: string) {
		return await this.getTagUseCase.execute(id);
	}

	@Patch(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Atualizar tag",
		description: "Atualiza uma tag existente",
	})
	@ApiParam({
		name: "id",
		description: "ID da tag",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiNotFoundResponse({
		description: "Tag não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiConflictResponse({
		description: "Já existe uma tag com esse nome",
		type: ConflictErrorResponseDto,
	})
	async updateTag(@Param("id") id: string, @Body() updateTagDto: UpdateTagDto) {
		return await this.updateTagUseCase.execute(id, updateTagDto);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "Excluir tag",
		description: "Remove uma tag existente",
	})
	@ApiParam({
		name: "id",
		description: "ID da tag",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	@ApiNotFoundResponse({
		description: "Tag não encontrada",
		type: NotFoundErrorResponseDto,
	})
	async deleteTag(@Param("id") id: string) {
		await this.deleteTagUseCase.execute(id);
		return { message: "Tag excluída com sucesso" };
	}

	@Post("works/:workId/tags/:tagId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "Adicionar tag a uma obra",
		description: "Associa uma tag existente a uma obra específica",
	})
	@ApiParam({
		name: "workId",
		description: "ID da obra",
		example: "123e4567-e89b-12d3-a456-426614174001",
	})
	@ApiParam({
		name: "tagId",
		description: "ID da tag",
		example: "123e4567-e89b-12d3-a456-426614174002",
	})
	@ApiNotFoundResponse({
		description: "Obra ou tag não encontrada",
		type: NotFoundErrorResponseDto,
	})
	@ApiConflictResponse({
		description: "Tag já está associada à obra",
		type: ConflictErrorResponseDto,
	})
	async addTagToWork(@Param("workId") workId: string, @Param("tagId") tagId: string) {
		return await this.addTagToWorkUseCase.execute(workId, tagId);
	}

	@Delete("works/:workId/tags/:tagId")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "Remover tag de uma obra",
		description: "Remove a associação entre uma tag e uma obra",
	})
	@ApiParam({
		name: "workId",
		description: "ID da obra",
		example: "123e4567-e89b-12d3-a456-426614174001",
	})
	@ApiParam({
		name: "tagId",
		description: "ID da tag",
		example: "123e4567-e89b-12d3-a456-426614174002",
	})
	@ApiNotFoundResponse({
		description: "Associação entre tag e obra não encontrada",
		type: NotFoundErrorResponseDto,
	})
	async removeTagFromWork(@Param("workId") workId: string, @Param("tagId") tagId: string) {
		await this.removeTagFromWorkUseCase.execute(workId, tagId);
		return { message: "Tag removida da obra com sucesso" };
	}

	@Get("works/:workId/tags")
	@ApiOperation({
		summary: "Listar tags de uma obra",
		description: "Lista todas as tags associadas a uma obra específica",
	})
	@ApiParam({
		name: "workId",
		description: "ID da obra",
		example: "123e4567-e89b-12d3-a456-426614174001",
	})
	@ApiNotFoundResponse({
		description: "Obra não encontrada",
		type: NotFoundErrorResponseDto,
	})
	async getWorkTags(@Param("workId") workId: string) {
		return await this.getWorkTagsUseCase.execute(workId);
	}

	@Get("filter")
	@ApiOperation({
		summary: "Filtrar obras por tags",
		description: "Retorna obras que possuem as tags especificadas",
	})
	@ApiQuery({
		name: "tagIds",
		description: "IDs das tags (separados por vírgula)",
		required: true,
		type: String,
	})
	async getWorksByTag(@Query("tagIds") tagIdsString: string) {
		const tagIds = tagIdsString.split(",");
		return await this.getWorksByTagUseCase.execute(tagIds);
	}
}
