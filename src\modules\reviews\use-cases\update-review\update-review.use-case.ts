import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { UpdateReviewDto } from "../../models/dtos";
import { IReview, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class UpdateReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(id: string, userId: string, updateReviewDto: UpdateReviewDto): Promise<IReview> {
		// Verificar se a review existe
		const review = await this.reviewRepository.findById(id);

		if (!review) {
			throw new ResourceNotFoundException("Avaliação", id);
		}

		// Verificar se a review pertence ao usuário atual
		if (review.userId !== userId) {
			throw new ResourceNotFoundException("Avaliação do usuário", `${id}:${userId}`);
		}

		// Atualizar a review
		return await this.reviewRepository.update(id, updateReviewDto);
	}
}
