import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { AddItemToRankingDto } from "../../models/dtos";
import { IRankingItem, IRankingItemRepository, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class AddItemToRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository,

		@Inject("IRankingItemRepository")
		private rankingItemRepository: IRankingItemRepository,

		@Inject("IWorkRepository")
		private workRepository: any // Usando any pois o IWorkRepository não foi importado
	) {}

	async execute(rankingId: string, userId: string, dto: AddItemToRankingDto): Promise<IRankingItem> {
		// Verificar se o ranking existe e pertence ao usuário
		const ranking = await this.rankingRepository.findById(rankingId);
		if (!ranking) {
			throw new ResourceNotFoundException("Ranking", rankingId);
		}
		if (ranking.userId !== userId) {
			throw new ResourceNotFoundException("Ranking do usuário", `${rankingId}:${userId}`);
		}

		// Verificar se a obra existe
		const work = await this.workRepository.findById(dto.workId);
		if (!work) {
			throw new ResourceNotFoundException("Obra", dto.workId);
		}

		// Verificar se a obra já está no ranking
		const existingItem = await this.rankingItemRepository.findByRankingIdAndWorkId(rankingId, dto.workId);
		if (existingItem) {
			throw new DuplicateResourceException("Obra no ranking", "workId", dto.workId);
		}

		// Determinar a posição do item no ranking
		let position = dto.position;

		if (!position) {
			const items = await this.rankingItemRepository.findByRankingId(rankingId);
			position = items.length > 0 ? items.length + 1 : 1;
		}

		// Criar o item no ranking
		const rankingItem = await this.rankingItemRepository.create({
			rankingId,
			workId: dto.workId,
			position,
			comment: dto.comment,
			rating: dto.rating,
		});

		// Incrementar o contador de itens no ranking
		await this.rankingRepository.incrementItemsCount(rankingId);

		return rankingItem;
	}
}
