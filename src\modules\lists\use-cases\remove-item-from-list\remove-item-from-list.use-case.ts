import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IListItemRepository, IListRepository } from "../../models/interfaces";

@Injectable()
export class RemoveItemFromListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository,
		@Inject("IListItemRepository")
		private readonly listItemRepository: IListItemRepository
	) {}

	async execute(userId: string, listId: string, itemId: string): Promise<void> {
		// Verificar se a lista existe e pertence ao usuário
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new ResourceNotFoundException("Lista", listId);
		}

		if (list.userId !== userId) {
			throw new ResourceNotFoundException("Lista do usuário", `${listId}:${userId}`);
		}
		// Verificar se o item existe e pertence à lista
		const item = await this.listItemRepository.findById(itemId);
		if (!item) {
			throw new ResourceNotFoundException("Item da lista", itemId);
		}

		if (item.listId !== listId) {
			throw new ResourceNotFoundException("Item da lista", `${itemId}:${listId}`);
		}

		await this.listItemRepository.delete(itemId);

		// Decrementar contador de itens na lista
		await this.listRepository.decrementItemsCount(listId);
	}
}
