import { ConflictException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { CreateUserDto } from "../../models/dtos/create-user.dto";
import { User } from "../../models/entities/user.entity";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";
import { CreateUserUseCase } from "./create-user.use-case";

describe("CreateUserUseCase", () => {
	let useCase: CreateUserUseCase;
	let userRepository: jest.Mocked<IUserRepository>;

	beforeEach(async () => {
		const mockUserRepository = {
			findByEmail: jest.fn(),
			findByUsername: jest.fn(),
			create: jest.fn(),
			findById: jest.fn(),
			update: jest.fn(),
			delete: jest.fn(),
			findAll: jest.fn(),
			findByUsernameOrEmail: jest.fn(),
		};

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				CreateUserUseCase,
				{
					provide: "IUserRepository",
					useValue: mockUserRepository,
				},
			],
		}).compile();

		useCase = module.get<CreateUserUseCase>(CreateUserUseCase);
		userRepository = module.get("IUserRepository");
	});

	describe("execute", () => {
		const createUserDto: CreateUserDto = {
			username: "testuser",
			email: "<EMAIL>",
			password: "password123",
			fullName: "Test User",
		};

		it("should create a user successfully", async () => {
			// Arrange
			userRepository.findByEmail.mockResolvedValue(null);
			userRepository.findByUsername.mockResolvedValue(null);

			const createdUser = {
				id: 1,
				...createUserDto,
				password: "hashedPassword",
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			} as User;

			userRepository.create.mockResolvedValue(createdUser);

			// Act
			const result = await useCase.execute(createUserDto);

			// Assert
			expect(userRepository.findByEmail).toHaveBeenCalledWith(createUserDto.email);
			expect(userRepository.findByUsername).toHaveBeenCalledWith(createUserDto.username);
			expect(userRepository.create).toHaveBeenCalledWith({
				...createUserDto,
				password: expect.any(String), // hashed password
			});
			expect(result).toEqual({
				id: 1,
				username: createUserDto.username,
				email: createUserDto.email,
				fullName: createUserDto.fullName,
				isActive: true,
				createdAt: expect.any(Date),
				updatedAt: expect.any(Date),
			});
			expect(result).not.toHaveProperty("password");
		});

		it("should throw ConflictException when email already exists", async () => {
			// Arrange
			const existingUser = { id: 1, email: createUserDto.email } as User;
			userRepository.findByEmail.mockResolvedValue(existingUser);

			// Act & Assert
			await expect(useCase.execute(createUserDto)).rejects.toThrow(new ConflictException("Email já está em uso"));
			expect(userRepository.findByUsername).not.toHaveBeenCalled();
			expect(userRepository.create).not.toHaveBeenCalled();
		});

		it("should throw ConflictException when username already exists", async () => {
			// Arrange
			userRepository.findByEmail.mockResolvedValue(null);
			const existingUser = { id: 1, username: createUserDto.username } as User;
			userRepository.findByUsername.mockResolvedValue(existingUser);

			// Act & Assert
			await expect(useCase.execute(createUserDto)).rejects.toThrow(new ConflictException("Nome de usuário já está em uso"));
			expect(userRepository.create).not.toHaveBeenCalled();
		});
	});
});
