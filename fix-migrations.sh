#!/usr/bin/env bash

# Script para resolver conflitos de migração
# Execute este script quando encontrar o erro "relação já existe"

echo "🔧 Resolvendo conflitos de migração..."

# Opção 1: Resetar banco de dados (CUIDADO: Apaga todos os dados)
echo "Opção 1: Resetar banco de dados completo"
echo "AVISO: Isso apagará TODOS OS DADOS do banco!"
echo "Execute apenas em ambiente de desenvolvimento!"
echo ""
echo "Para resetar o banco:"
echo "docker-compose down -v"
echo "docker-compose up -d postgres"
echo "npm run migration:run"
echo ""

# Opção 2: Marcar migrações como executadas manualmente
echo "Opção 2: Marcar migrações como já executadas (se as tabelas já existem)"
echo ""
echo "1. Conecte no banco de dados:"
echo "   docker exec -it [container-postgres] psql -U postgres -d backend_test"
echo ""
echo "2. Verificar migrações já executadas:"
echo "   SELECT * FROM migrations ORDER BY id;"
echo ""
echo "3. Se a migração 'AddRankingsSchema1718478073827' não estiver listada, mas as tabelas existem:"
echo "   INSERT INTO migrations (timestamp, name) VALUES (1718478073827, 'AddRankingsSchema1718478073827');"
echo ""
echo "4. Verificar se todas as tabelas existem:"
echo "   \\dt"
echo ""
echo "5. Sair do psql:"
echo "   \\q"
echo ""

# Opção 3: Executar migrações manualmente
echo "Opção 3: Executar migrações manualmente"
echo "npm run typeorm migration:run"
echo ""

# Opção 4: Reverter e reexecutar migração específica
echo "Opção 4: Reverter e reexecutar migração específica"
echo "npm run typeorm migration:revert"
echo "npm run typeorm migration:run"
