import { ICommentReaction } from "./ICommentReaction.interface";

export interface IReviewComment {
	id: string;
	reviewId: string;
	userId: string;
	content: string;
	parentId?: string;
	likes: number;
	dislikes: number;
	replies?: IReviewComment[];
	reactions?: ICommentReaction[];
	createdAt: Date;
	updatedAt: Date;
}

export interface IReviewCommentFilters {
	reviewId?: string;
	userId?: string;
	parentId?: string;
	skip?: number;
	take?: number;
	sort?: string;
	order?: "ASC" | "DESC";
}
