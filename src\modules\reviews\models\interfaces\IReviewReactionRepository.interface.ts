import { IReviewReaction } from "./IReviewReaction.interface";

export interface IReviewReactionRepository {
	create(reaction: Partial<IReviewReaction>): Promise<IReviewReaction>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<IReviewReaction>;
	findByReviewAndUser(reviewId: string, userId: string): Promise<IReviewReaction>;
	findAllByReviewId(reviewId: string): Promise<IReviewReaction[]>;
}
