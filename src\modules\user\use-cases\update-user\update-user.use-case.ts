import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { UpdateUserDto } from "../../models/dtos/update-user.dto";
import { UserResponseDto } from "../../models/dtos/user-response.dto";
import { User } from "../../models/entities/user.entity";
import { IUserRepository } from "../../models/interfaces/user-repository.interface";

@Injectable()
export class UpdateUserUseCase {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async execute(id: number, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
		const user = await this.userRepository.findById(id);
		if (!user) throw new ResourceNotFoundException("Usuário", id);
		const updatedUser = await this.userRepository.update(id, updateUserDto);
		return this.mapToResponseDto(updatedUser);
	}

	private mapToResponseDto(user: User): UserResponseDto {
		const { password, ...userWithoutPassword } = user;
		return userWithoutPassword as UserResponseDto;
	}
}
