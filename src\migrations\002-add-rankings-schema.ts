import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRankingsSchema1718478073827 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Verificar se a tabela rankings já existe
		const rankingsTableExists = await queryRunner.hasTable("rankings");

		if (!rankingsTableExists) {
			// Criar a tabela de rankings
			await queryRunner.query(`
				CREATE TABLE "rankings" (
					"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
					"userId" uuid NOT NULL,
					"name" varchar(100) NOT NULL,
					"description" text,
					"isPublic" boolean NOT NULL DEFAULT false,
					"coverImage" varchar(255),
					"itemsCount" integer NOT NULL DEFAULT 0,
					"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
					"updatedAt" TIMESTAMP NOT NULL DEFAULT now()
				)
			`);

			// Criar índice para userId + name (para garantir unicidade)
			await queryRunner.query(`
				CREATE UNIQUE INDEX "IDX_rankings_userId_name" ON "rankings" ("userId", "name")
			`);
		}

		// Verificar se a tabela ranking_items já existe
		const rankingItemsTableExists = await queryRunner.hasTable("ranking_items");

		if (!rankingItemsTableExists) {
			// Criar a tabela de itens do ranking
			await queryRunner.query(`
				CREATE TABLE "ranking_items" (
					"id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
					"rankingId" uuid NOT NULL,
					"workId" uuid NOT NULL,
					"position" integer NOT NULL,
					"comment" text,
					"rating" integer,
					"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
					"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
					CONSTRAINT "FK_ranking_items_rankings" FOREIGN KEY ("rankingId") REFERENCES "rankings" ("id") ON DELETE CASCADE
				)
			`);

			// Criar índice para rankingId + position
			await queryRunner.query(`
				CREATE INDEX "IDX_ranking_items_rankingId_position" ON "ranking_items" ("rankingId", "position")
			`);

			// Criar índice para rankingId + workId (para garantir unicidade)
			await queryRunner.query(`
				CREATE UNIQUE INDEX "IDX_ranking_items_rankingId_workId" ON "ranking_items" ("rankingId", "workId")
			`);
		}
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover índices
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_ranking_items_rankingId_workId"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_ranking_items_rankingId_position"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "IDX_rankings_userId_name"`);

		// Remover tabelas
		await queryRunner.query(`DROP TABLE IF EXISTS "ranking_items"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "rankings"`);
	}
}
