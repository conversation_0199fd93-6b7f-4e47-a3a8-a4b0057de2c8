import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException } from "src/shared/exceptions/business.exceptions";
import { ICreateListRequest, IList, IListRepository } from "../../models/interfaces";

@Injectable()
export class CreateListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: string, data: Omit<ICreateListRequest, "userId">): Promise<IList> {
		// Verificar se já existe uma lista com o mesmo nome para este usuário
		const existingList = await this.listRepository.findByUserAndName(userId, data.name);
		if (existingList) {
			throw new DuplicateResourceException("Lista", "name", data.name);
		}

		const createData: ICreateListRequest = {
			...data,
			userId,
		};

		return await this.listRepository.create(createData);
	}
}
