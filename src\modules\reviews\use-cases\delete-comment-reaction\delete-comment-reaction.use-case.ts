import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { ICommentReactionRepository, IReviewCommentRepository } from "../../models/interfaces";

@Injectable()
export class DeleteCommentReactionUseCase {
	constructor(
		@Inject("ICommentReactionRepository")
		private readonly reactionRepository: ICommentReactionRepository,

		@Inject("IReviewCommentRepository")
		private readonly commentRepository: IReviewCommentRepository
	) {}

	async execute(commentId: string, userId: string): Promise<void> {
		// Verificar se a reação existe
		const reaction = await this.reactionRepository.findByCommentAndUser(commentId, userId);
		if (!reaction) {
			throw new ResourceNotFoundException("Reação", `${commentId}:${userId}`);
		}

		// Atualizar os contadores no comentário
		if (reaction.isLike) {
			await this.commentRepository.decrementLikes(commentId);
		} else {
			await this.commentRepository.decrementDislikes(commentId);
		}

		// Excluir a reação
		await this.reactionRepository.delete(reaction.id);
	}
}
