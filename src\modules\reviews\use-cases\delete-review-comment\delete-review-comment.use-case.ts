import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IReviewCommentRepository } from "../../models/interfaces";

@Injectable()
export class DeleteReviewCommentUseCase {
	constructor(
		@Inject("IReviewCommentRepository")
		private readonly commentRepository: IReviewCommentRepository
	) {}

	async execute(id: string, userId: string): Promise<void> {
		// Verificar se o comentário existe
		const comment = await this.commentRepository.findById(id);

		if (!comment) {
			throw new ResourceNotFoundException("Comentário", id);
		}

		// Verificar se o comentário pertence ao usuário atual
		if (comment.userId !== userId) {
			throw new ResourceNotFoundException("Comentário do usuário", `${id}:${userId}`);
		}

		// Excluir o comentário
		await this.commentRepository.delete(id);
	}
}
