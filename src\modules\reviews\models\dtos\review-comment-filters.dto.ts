import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsNumber, IsOptional, IsString, IsUUID, Min } from "class-validator";

export class ReviewCommentFiltersDto {
	@ApiProperty({
		description: "Filtrar por ID da review",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
		required: false,
	})
	@IsUUID()
	@IsOptional()
	reviewId?: string;

	@ApiProperty({
		description: "Filtrar por ID do usuário",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
		required: false,
	})
	@IsUUID()
	@IsOptional()
	userId?: string;

	@ApiProperty({
		description: "Filtrar por ID do comentário pai (para listar apenas comentários de primeiro nível ou respostas)",
		example: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
		required: false,
	})
	@IsUUID()
	@IsOptional()
	parentId?: string;

	@ApiProperty({
		description: "Quantidade de itens a pular",
		example: 0,
		required: false,
		default: 0,
	})
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Type(() => Number)
	skip?: number;

	@ApiProperty({
		description: "Quantidade de itens a retornar",
		example: 10,
		required: false,
		default: 10,
	})
	@IsNumber()
	@IsOptional()
	@Min(1)
	@Type(() => Number)
	take?: number;

	@ApiProperty({
		description: "Campo para ordenação",
		example: "createdAt",
		required: false,
		default: "createdAt",
	})
	@IsString()
	@IsOptional()
	sort?: string;

	@ApiProperty({
		description: "Ordem da ordenação",
		example: "DESC",
		required: false,
		enum: ["ASC", "DESC"],
		default: "DESC",
	})
	@IsEnum(["ASC", "DESC"])
	@IsOptional()
	order?: "ASC" | "DESC";
}
