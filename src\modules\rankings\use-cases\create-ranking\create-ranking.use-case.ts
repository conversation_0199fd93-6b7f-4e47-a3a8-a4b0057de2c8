import { Inject, Injectable } from "@nestjs/common";
import { CreateRankingDto } from "../../models/dtos";
import { IRanking, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class CreateRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(userId: string, createRankingDto: CreateRankingDto): Promise<IRanking> {
		const ranking = {
			userId,
			name: createRankingDto.name,
			description: createRankingDto.description,
			isPublic: createRankingDto.isPublic || false,
			coverImage: createRankingDto.coverImage,
			itemsCount: 0,
		};

		return await this.rankingRepository.create(ranking);
	}
}
