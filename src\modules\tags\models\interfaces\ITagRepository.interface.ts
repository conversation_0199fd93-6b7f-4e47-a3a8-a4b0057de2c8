import { ITag } from "./ITag.interface";

export interface ITagFilters {
	search?: string;
	sortBy?: "name" | "worksCount" | "createdAt";
	sortOrder?: "ASC" | "DESC";
}

export interface ITagRepository {
	create(tag: Partial<ITag>): Promise<ITag>;
	update(id: string, tag: Partial<ITag>): Promise<ITag>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<ITag>;
	findByName(name: string): Promise<ITag>;
	findAll(filters?: ITagFilters): Promise<ITag[]>;
	findPopularTags(limit?: number): Promise<ITag[]>;
	incrementWorksCount(id: string): Promise<void>;
	decrementWorksCount(id: string): Promise<void>;
}
